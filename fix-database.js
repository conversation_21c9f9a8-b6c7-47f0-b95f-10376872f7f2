#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const DatabaseSetup = require('./setup-database');

async function fixDatabase() {
    console.log('🔧 Fixing database permissions and recreating if needed...');
    
    const dbPath = path.join(__dirname, 'products.sqlite');
    const backupPath = path.join(__dirname, 'products.sqlite.backup');
    
    try {
        // First, let's try to backup existing data
        console.log('📋 Backing up existing data...');
        
        if (fs.existsSync(dbPath)) {
            // Try to read existing data
            const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY);
            
            const exportData = await new Promise((resolve, reject) => {
                const data = {
                    stores: [],
                    categories: [],
                    products: [],
                    price_history: [],
                    scraping_logs: []
                };
                
                let completed = 0;
                const tables = ['stores', 'categories', 'products', 'price_history', 'scraping_logs'];
                
                tables.forEach(table => {
                    db.all(`SELECT * FROM ${table}`, (err, rows) => {
                        if (err) {
                            console.warn(`Warning: Could not read ${table}: ${err.message}`);
                            rows = [];
                        }
                        data[table] = rows || [];
                        completed++;
                        
                        if (completed === tables.length) {
                            db.close();
                            resolve(data);
                        }
                    });
                });
            });
            
            console.log(`✅ Backed up ${exportData.stores.length} stores, ${exportData.categories.length} categories, ${exportData.products.length} products`);
            
            // Save backup as JSON
            fs.writeFileSync(backupPath + '.json', JSON.stringify(exportData, null, 2));
            console.log(`💾 Data saved to ${backupPath}.json`);
            
            // Remove the problematic database file
            try {
                fs.unlinkSync(dbPath);
                console.log('🗑️ Removed old database file');
            } catch (error) {
                console.error('❌ Could not remove old database file:', error.message);
                console.log('🔄 Trying alternative approach...');
                
                // Try moving it instead
                const tempPath = dbPath + '.old.' + Date.now();
                fs.renameSync(dbPath, tempPath);
                console.log(`📦 Moved old database to ${tempPath}`);
            }
            
            // Recreate database
            console.log('🏗️ Creating new database...');
            const dbSetup = new DatabaseSetup();
            await dbSetup.initialize();
            await dbSetup.createTables();
            await dbSetup.insertInitialData();
            
            // Restore data
            console.log('📥 Restoring data...');
            
            // Restore stores
            for (const store of exportData.stores) {
                await new Promise((resolve, reject) => {
                    dbSetup.db.run(
                        'INSERT OR REPLACE INTO stores (id, name, base_url, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
                        [store.id, store.name, store.base_url, store.created_at, store.updated_at],
                        (err) => err ? reject(err) : resolve()
                    );
                });
            }
            
            // Restore categories
            for (const category of exportData.categories) {
                await new Promise((resolve, reject) => {
                    dbSetup.db.run(
                        'INSERT OR REPLACE INTO categories (id, store_id, name, url, parent_id, level, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                        [category.id, category.store_id, category.name, category.url, category.parent_id, category.level, category.created_at, category.updated_at],
                        (err) => err ? reject(err) : resolve()
                    );
                });
            }
            
            // Restore products
            for (const product of exportData.products) {
                await new Promise((resolve, reject) => {
                    dbSetup.db.run(
                        'INSERT OR REPLACE INTO products (id, store_id, category_id, name, price, original_price, product_url, image_url, description, brand, sku, availability_status, in_stock, created_at, updated_at, last_scraped) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                        [product.id, product.store_id, product.category_id, product.name, product.price, product.original_price, product.product_url, product.image_url, product.description, product.brand, product.sku, product.availability_status, product.in_stock, product.created_at, product.updated_at, product.last_scraped],
                        (err) => err ? reject(err) : resolve()
                    );
                });
            }
            
            // Restore price history
            for (const price of exportData.price_history) {
                await new Promise((resolve, reject) => {
                    dbSetup.db.run(
                        'INSERT OR REPLACE INTO price_history (id, product_id, price, original_price, recorded_at) VALUES (?, ?, ?, ?, ?)',
                        [price.id, price.product_id, price.price, price.original_price, price.recorded_at],
                        (err) => err ? reject(err) : resolve()
                    );
                });
            }
            
            // Restore scraping logs
            for (const log of exportData.scraping_logs) {
                await new Promise((resolve, reject) => {
                    dbSetup.db.run(
                        'INSERT OR REPLACE INTO scraping_logs (id, store_id, category_id, status, products_found, products_updated, errors_count, start_time, end_time, error_message) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                        [log.id, log.store_id, log.category_id, log.status, log.products_found, log.products_updated, log.errors_count, log.start_time, log.end_time, log.error_message],
                        (err) => err ? reject(err) : resolve()
                    );
                });
            }
            
            await dbSetup.close();
            console.log('✅ Data restoration completed');
            
        } else {
            // No existing database, just create new one
            console.log('🏗️ Creating new database...');
            const dbSetup = new DatabaseSetup();
            await dbSetup.initialize();
            await dbSetup.createTables();
            await dbSetup.insertInitialData();
            await dbSetup.close();
        }
        
        // Set proper permissions
        if (fs.existsSync(dbPath)) {
            fs.chmodSync(dbPath, 0o664);
            console.log('🔐 Set proper file permissions');
        }
        
        console.log('🎉 Database fix completed successfully!');
        
        // Test the database
        console.log('🧪 Testing database...');
        const testDb = new sqlite3.Database(dbPath);
        
        await new Promise((resolve, reject) => {
            testDb.run('INSERT INTO scraping_logs (store_id, status) VALUES (1, "test")', (err) => {
                if (err) {
                    reject(err);
                } else {
                    testDb.run('DELETE FROM scraping_logs WHERE status = "test"', (err2) => {
                        testDb.close();
                        if (err2) reject(err2);
                        else resolve();
                    });
                }
            });
        });
        
        console.log('✅ Database write test passed!');
        
    } catch (error) {
        console.error('❌ Error fixing database:', error.message);
        console.log('\n🔄 Alternative solution: Delete and recreate database');
        console.log('Run: rm -f products.sqlite && node setup-database.js');
        process.exit(1);
    }
}

if (require.main === module) {
    fixDatabase().catch(error => {
        console.error('Fatal error:', error.message);
        process.exit(1);
    });
}

module.exports = fixDatabase;
