#!/usr/bin/env node

const sqlite3 = require("sqlite3").verbose();
const fs = require("fs").promises;
const path = require("path");
const winston = require("winston");

// Setup logger
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level.toUpperCase()}] ${message}`;
    })
  ),
  transports: [new winston.transports.Console()],
});

class DataExporter {
  constructor() {
    this.dbPath = path.join(__dirname, "products_working.sqlite");
    this.db = null;
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          reject(err);
        } else {
          logger.info("Connected to SQLite database for export");
          resolve();
        }
      });
    });
  }

  async query(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async exportCompleteData() {
    logger.info("🚀 Starting complete data export...");

    try {
      // Get all stores
      const stores = await this.query("SELECT * FROM stores ORDER BY name");
      logger.info(`Found ${stores.length} stores`);

      // Get all categories with store info
      const categories = await this.query(`
                SELECT c.*, s.name as store_name, s.url as store_url
                FROM categories c
                JOIN stores s ON c.store_id = s.id
                ORDER BY s.name, c.level, c.name
            `);
      logger.info(`Found ${categories.length} categories`);

      // Get all products with full details
      const products = await this.query(`
                SELECT 
                    p.*,
                    s.name as store_name,
                    s.url as store_url,
                    c.name as category_name,
                    c.url as category_url
                FROM products p
                JOIN stores s ON p.store_id = s.id
                JOIN categories c ON p.category_id = c.id
                ORDER BY s.name, c.name, p.name
            `);
      logger.info(`Found ${products.length} products`);

      // Get price history
      const priceHistory = await this.query(`
                SELECT 
                    ph.*,
                    p.name as product_name,
                    p.product_url,
                    s.name as store_name
                FROM price_history ph
                JOIN products p ON ph.product_id = p.id
                JOIN stores s ON p.store_id = s.id
                ORDER BY s.name, p.name, ph.recorded_at DESC
            `);
      logger.info(`Found ${priceHistory.length} price history records`);

      // Get scraping logs
      const scrapingLogs = await this.query(`
                SELECT 
                    sl.*,
                    s.name as store_name
                FROM scraping_logs sl
                JOIN stores s ON sl.store_id = s.id
                ORDER BY sl.started_at DESC
                LIMIT 100
            `);
      logger.info(`Found ${scrapingLogs.length} recent scraping logs`);

      // Build comprehensive data structure
      const exportData = {
        export_info: {
          generated_at: new Date().toISOString(),
          total_stores: stores.length,
          total_categories: categories.length,
          total_products: products.length,
          total_price_records: priceHistory.length,
          recent_scraping_logs: scrapingLogs.length,
        },
        stores: stores.map((store) => ({
          ...store,
          categories: categories.filter((c) => c.store_id === store.id),
          products: products.filter((p) => p.store_id === store.id),
          recent_logs: scrapingLogs
            .filter((l) => l.store_id === store.id)
            .slice(0, 10),
        })),
        products_by_store: this.groupProductsByStore(products),
        price_trends: this.analyzePriceTrends(priceHistory),
        statistics: await this.generateStatistics(),
      };

      return exportData;
    } catch (error) {
      logger.error(`Error exporting data: ${error.message}`);
      throw error;
    }
  }

  groupProductsByStore(products) {
    const grouped = {};

    products.forEach((product) => {
      if (!grouped[product.store_name]) {
        grouped[product.store_name] = {
          total_products: 0,
          categories: {},
          products: [],
        };
      }

      grouped[product.store_name].total_products++;
      grouped[product.store_name].products.push(product);

      if (!grouped[product.store_name].categories[product.category_name]) {
        grouped[product.store_name].categories[product.category_name] = {
          product_count: 0,
          products: [],
        };
      }

      grouped[product.store_name].categories[product.category_name]
        .product_count++;
      grouped[product.store_name].categories[
        product.category_name
      ].products.push({
        id: product.id,
        name: product.name,
        price: product.price,
        original_price: product.original_price,
        product_url: product.product_url,
        image_url: product.image_url,
        brand: product.brand,
        in_stock: product.in_stock,
        created_at: product.created_at,
        updated_at: product.updated_at,
      });
    });

    return grouped;
  }

  analyzePriceTrends(priceHistory) {
    const trends = {};

    priceHistory.forEach((record) => {
      const key = `${record.store_name}_${record.product_name}`;

      if (!trends[key]) {
        trends[key] = {
          product_name: record.product_name,
          store_name: record.store_name,
          product_url: record.product_url,
          price_changes: [],
          current_price: null,
          lowest_price: null,
          highest_price: null,
          price_change_count: 0,
        };
      }

      trends[key].price_changes.push({
        price: record.price,
        original_price: record.original_price,
        recorded_at: record.recorded_at,
      });

      if (record.price) {
        if (!trends[key].current_price)
          trends[key].current_price = record.price;
        if (
          !trends[key].lowest_price ||
          record.price < trends[key].lowest_price
        ) {
          trends[key].lowest_price = record.price;
        }
        if (
          !trends[key].highest_price ||
          record.price > trends[key].highest_price
        ) {
          trends[key].highest_price = record.price;
        }
      }
    });

    // Calculate price change counts
    Object.values(trends).forEach((trend) => {
      trend.price_change_count = trend.price_changes.length;
      trend.price_changes.sort(
        (a, b) => new Date(b.recorded_at) - new Date(a.recorded_at)
      );
    });

    return trends;
  }

  async generateStatistics() {
    const stats = {};

    // Products per store
    const productsByStore = await this.query(`
            SELECT s.name as store_name, COUNT(p.id) as product_count
            FROM stores s
            LEFT JOIN products p ON s.id = p.store_id
            GROUP BY s.id, s.name
            ORDER BY product_count DESC
        `);

    // Categories per store
    const categoriesByStore = await this.query(`
            SELECT s.name as store_name, COUNT(c.id) as category_count
            FROM stores s
            LEFT JOIN categories c ON s.id = c.store_id
            GROUP BY s.id, s.name
            ORDER BY category_count DESC
        `);

    // Price ranges per store
    const priceRanges = await this.query(`
            SELECT 
                s.name as store_name,
                MIN(p.price) as min_price,
                MAX(p.price) as max_price,
                AVG(p.price) as avg_price,
                COUNT(p.id) as products_with_price
            FROM stores s
            LEFT JOIN products p ON s.id = p.store_id
            WHERE p.price IS NOT NULL
            GROUP BY s.id, s.name
        `);

    // Recent activity
    const recentActivity = await this.query(`
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as products_added
            FROM products
            WHERE created_at >= date('now', '-30 days')
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        `);

    stats.products_by_store = productsByStore;
    stats.categories_by_store = categoriesByStore;
    stats.price_ranges = priceRanges;
    stats.recent_activity = recentActivity;

    return stats;
  }

  async saveToFile(data, filename) {
    const exportDir = path.join(__dirname, "exports");

    // Create exports directory if it doesn't exist
    try {
      await fs.access(exportDir);
    } catch {
      await fs.mkdir(exportDir, { recursive: true });
    }

    const filePath = path.join(exportDir, filename);
    const jsonData = JSON.stringify(data, null, 2);

    await fs.writeFile(filePath, jsonData, "utf8");
    logger.info(`✅ Data exported to: ${filePath}`);

    // Also save a minified version
    const minifiedPath = path.join(
      exportDir,
      filename.replace(".json", ".min.json")
    );
    await fs.writeFile(minifiedPath, JSON.stringify(data), "utf8");
    logger.info(`✅ Minified version saved to: ${minifiedPath}`);

    return filePath;
  }

  async close() {
    if (this.db) {
      return new Promise((resolve) => {
        this.db.close((err) => {
          if (err) {
            logger.error(`Error closing database: ${err.message}`);
          } else {
            logger.info("Database connection closed");
          }
          resolve();
        });
      });
    }
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
const command = args[0] || "complete";

async function exportCompleteData(exporter) {
  logger.info("🚀 Starting complete data export...");

  try {
    // Get all stores
    const stores = await exporter.query("SELECT * FROM stores ORDER BY name");
    logger.info(`Found ${stores.length} stores`);

    // Get all categories with store info
    const categories = await exporter.query(`
      SELECT c.*, s.name as store_name, s.base_url as store_url
      FROM categories c
      JOIN stores s ON c.store_id = s.id
      ORDER BY s.name, c.level, c.name
    `);
    logger.info(`Found ${categories.length} categories`);

    // Get all products with full details
    const products = await exporter.query(`
      SELECT
        p.*,
        s.name as store_name,
        s.base_url as store_url,
        c.name as category_name,
        c.url as category_url
      FROM products p
      JOIN stores s ON p.store_id = s.id
      JOIN categories c ON p.category_id = c.id
      ORDER BY s.name, c.name, p.name
    `);
    logger.info(`Found ${products.length} products`);

    // Get price history
    const priceHistory = await exporter.query(`
      SELECT
        ph.*,
        p.name as product_name,
        p.product_url,
        s.name as store_name
      FROM price_history ph
      JOIN products p ON ph.product_id = p.id
      JOIN stores s ON p.store_id = s.id
      ORDER BY s.name, p.name, ph.recorded_at DESC
    `);
    logger.info(`Found ${priceHistory.length} price history records`);

    // Get scraping logs
    const scrapingLogs = await exporter.query(`
      SELECT
        sl.*,
        s.name as store_name
      FROM scraping_logs sl
      JOIN stores s ON sl.store_id = s.id
      ORDER BY sl.start_time DESC
      LIMIT 100
    `);
    logger.info(`Found ${scrapingLogs.length} recent scraping logs`);

    // Build comprehensive data structure
    const exportData = {
      export_info: {
        generated_at: new Date().toISOString(),
        total_stores: stores.length,
        total_categories: categories.length,
        total_products: products.length,
        total_price_records: priceHistory.length,
        recent_scraping_logs: scrapingLogs.length,
      },
      stores: stores.map((store) => ({
        ...store,
        categories: categories.filter((c) => c.store_id === store.id),
        products: products.filter((p) => p.store_id === store.id),
        recent_logs: scrapingLogs
          .filter((l) => l.store_id === store.id)
          .slice(0, 10),
      })),
      products_by_store: exporter.groupProductsByStore(products),
      price_trends: exporter.analyzePriceTrends(priceHistory),
      statistics: await exporter.generateStatistics(),
    };

    // Generate filename with timestamp
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .split("T")[0];
    const filename = `scraping-data-complete-${timestamp}.json`;

    const filePath = await exporter.saveToFile(exportData, filename);

    // Print summary
    logger.info("\n📋 EXPORT SUMMARY:");
    logger.info(`📁 File: ${filePath}`);
    logger.info(`🏪 Stores: ${exportData.export_info.total_stores}`);
    logger.info(`📂 Categories: ${exportData.export_info.total_categories}`);
    logger.info(`📦 Products: ${exportData.export_info.total_products}`);
    logger.info(
      `💰 Price Records: ${exportData.export_info.total_price_records}`
    );
    logger.info(
      `📊 File Size: ${(
        JSON.stringify(exportData).length /
        1024 /
        1024
      ).toFixed(2)} MB`
    );

    // Print store breakdown
    logger.info("\n🏪 STORE BREAKDOWN:");
    exportData.stores.forEach((store) => {
      const productCount =
        exportData.products_by_store[store.name]?.total_products || 0;
      const categoryCount = store.categories.length;
      logger.info(
        `  ${store.name}: ${productCount} products, ${categoryCount} categories`
      );
    });

    logger.info("\n🎉 Export completed successfully!");
  } catch (error) {
    logger.error(`Error exporting data: ${error.message}`);
    throw error;
  }
}

async function handleCommand() {
  const exporter = new DataExporter();

  try {
    await exporter.initialize();

    switch (command) {
      case "complete":
      case "all":
        await exportCompleteData(exporter);
        break;

      case "products":
        await exportProductsOnly(exporter);
        break;

      case "stores":
        await exportStoresOnly(exporter);
        break;

      case "prices":
        await exportPricesOnly(exporter);
        break;

      case "stats":
        await exportStatsOnly(exporter);
        break;

      default:
        logger.info("Usage:");
        logger.info(
          "  node export-data.js complete  - Export everything (default)"
        );
        logger.info("  node export-data.js products  - Export products only");
        logger.info(
          "  node export-data.js stores    - Export stores and categories"
        );
        logger.info("  node export-data.js prices    - Export price history");
        logger.info("  node export-data.js stats     - Export statistics only");
        break;
    }
  } finally {
    await exporter.close();
  }
}

async function exportProductsOnly(exporter) {
  logger.info("📦 Exporting products only...");

  const products = await exporter.query(`
        SELECT
            p.*,
            s.name as store_name,
            c.name as category_name
        FROM products p
        JOIN stores s ON p.store_id = s.id
        JOIN categories c ON p.category_id = c.id
        ORDER BY s.name, c.name, p.name
    `);

  const data = {
    export_type: "products_only",
    generated_at: new Date().toISOString(),
    total_products: products.length,
    products: products,
  };

  const timestamp = new Date().toISOString().split("T")[0];
  await exporter.saveToFile(data, `products-${timestamp}.json`);
}

async function exportStoresOnly(exporter) {
  logger.info("🏪 Exporting stores and categories...");

  const stores = await exporter.query("SELECT * FROM stores ORDER BY name");
  const categories = await exporter.query(`
        SELECT c.*, s.name as store_name
        FROM categories c
        JOIN stores s ON c.store_id = s.id
        ORDER BY s.name, c.level, c.name
    `);

  const data = {
    export_type: "stores_and_categories",
    generated_at: new Date().toISOString(),
    stores: stores.map((store) => ({
      ...store,
      categories: categories.filter((c) => c.store_id === store.id),
    })),
  };

  const timestamp = new Date().toISOString().split("T")[0];
  await exporter.saveToFile(data, `stores-categories-${timestamp}.json`);
}

async function exportPricesOnly(exporter) {
  logger.info("💰 Exporting price history...");

  const priceHistory = await exporter.query(`
        SELECT
            ph.*,
            p.name as product_name,
            p.product_url,
            s.name as store_name,
            c.name as category_name
        FROM price_history ph
        JOIN products p ON ph.product_id = p.id
        JOIN stores s ON p.store_id = s.id
        JOIN categories c ON p.category_id = c.id
        ORDER BY s.name, p.name, ph.recorded_at DESC
    `);

  const data = {
    export_type: "price_history",
    generated_at: new Date().toISOString(),
    total_records: priceHistory.length,
    price_history: priceHistory,
  };

  const timestamp = new Date().toISOString().split("T")[0];
  await exporter.saveToFile(data, `price-history-${timestamp}.json`);
}

async function exportStatsOnly(exporter) {
  logger.info("📊 Exporting statistics only...");

  const stats = await exporter.generateStatistics();

  const data = {
    export_type: "statistics_only",
    generated_at: new Date().toISOString(),
    statistics: stats,
  };

  const timestamp = new Date().toISOString().split("T")[0];
  await exporter.saveToFile(data, `statistics-${timestamp}.json`);
}

if (require.main === module) {
  handleCommand().catch((error) => {
    console.error(`Unhandled error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = DataExporter;
