#!/usr/bin/env node

const ShoppiScraper = require("./lib/ShoppiScraper");
const winston = require("winston");

// Setup test logger
const logger = winston.createLogger({
  level: "debug",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level.toUpperCase()}] ${message}`;
    })
  ),
  transports: [new winston.transports.Console({ level: "debug" })],
});

class ProductExtractionDebugger {
  constructor() {
    this.scraper = null;
  }

  async debugProductExtraction() {
    logger.info("Starting Product Extraction Debug");
    logger.info("=".repeat(50));

    try {
      this.scraper = new ShoppiScraper();
      await this.scraper.initialize();

      // Navigate to a specific category page
      const categoryUrl =
        "https://shoppi.tn/site/produits/7/Ordinateur-Portable";
      logger.info(`Navigating to: ${categoryUrl}`);

      await this.scraper.safeNavigate(categoryUrl);
      await this.scraper.delay(5000); // Wait longer for page to load

      // Debug: Check what elements exist on the page
      const pageInfo = await this.scraper.page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          bodyText: document.body.innerText.substring(0, 500),
          hasProducts: document.querySelectorAll(".col-md-3").length > 0,
          colMd3Count: document.querySelectorAll(".col-md-3").length,
          colSm6Count: document.querySelectorAll(".col-sm-6").length,
          productItemCount: document.querySelectorAll(".product-item").length,
          productCardCount: document.querySelectorAll(".product-card").length,
          detailLinksCount: document.querySelectorAll(
            'a[href*="/site/details/"]'
          ).length,
          allLinksCount: document.querySelectorAll("a").length,
        };
      });

      logger.info("Page Information:");
      logger.info(`  Title: ${pageInfo.title}`);
      logger.info(`  URL: ${pageInfo.url}`);
      logger.info(`  Has Products: ${pageInfo.hasProducts}`);
      logger.info(`  .col-md-3 elements: ${pageInfo.colMd3Count}`);
      logger.info(`  .col-sm-6 elements: ${pageInfo.colSm6Count}`);
      logger.info(`  .product-item elements: ${pageInfo.productItemCount}`);
      logger.info(`  .product-card elements: ${pageInfo.productCardCount}`);
      logger.info(`  Detail links: ${pageInfo.detailLinksCount}`);
      logger.info(`  Total links: ${pageInfo.allLinksCount}`);

      // Try different selectors to find products
      const selectorTests = [
        ".col-md-3",
        ".col-sm-6",
        ".product-item",
        ".product-card",
        'div[class*="col-"]',
        'div[class*="product"]',
        '[href*="/site/details/"]',
      ];

      for (const selector of selectorTests) {
        const count = await this.scraper.page.evaluate((sel) => {
          return document.querySelectorAll(sel).length;
        }, selector);

        logger.info(`  Selector "${selector}": ${count} elements`);

        if (count > 0 && count < 50) {
          // Reasonable number for debugging
          const sampleElements = await this.scraper.page.evaluate((sel) => {
            const elements = document.querySelectorAll(sel);
            return Array.from(elements)
              .slice(0, 3)
              .map((el) => ({
                tagName: el.tagName,
                className: el.className,
                innerHTML: el.innerHTML.substring(0, 200),
                textContent: el.textContent.trim().substring(0, 100),
              }));
          }, selector);

          logger.info(`    Sample elements for "${selector}":`);
          sampleElements.forEach((el, index) => {
            logger.info(`      ${index + 1}. ${el.tagName}.${el.className}`);
            logger.info(`         Text: ${el.textContent}`);
          });
        }
      }

      // Try to find product links specifically
      const productLinks = await this.scraper.page.evaluate(() => {
        const links = document.querySelectorAll('a[href*="/site/details/"]');
        return Array.from(links)
          .slice(0, 5)
          .map((link) => ({
            href: link.href,
            text: link.textContent.trim(),
            parentClass: link.parentElement
              ? link.parentElement.className
              : "no-parent",
          }));
      });

      logger.info(`Found ${productLinks.length} product detail links:`);
      productLinks.forEach((link, index) => {
        logger.info(`  ${index + 1}. ${link.text} - ${link.href}`);
        logger.info(`     Parent class: ${link.parentClass}`);
      });

      // Try to extract product information using different approaches
      if (productLinks.length > 0) {
        logger.info("Testing product detail extraction...");
        const firstProductUrl = productLinks[0].href;
        logger.info(`Navigating to product: ${firstProductUrl}`);

        await this.scraper.safeNavigate(firstProductUrl);
        await this.scraper.delay(3000);

        const productPageInfo = await this.scraper.page.evaluate(() => {
          const selectors = {
            h1: document.querySelector("h1"),
            h2: document.querySelector("h2"),
            title: document.querySelector(".title"),
            productTitle: document.querySelector(".product-title"),
            price: document.querySelector(".prix"),
            priceAlt: document.querySelector(".price"),
            description: document.querySelector(".description"),
            productDesc: document.querySelector(".product-description"),
          };

          const result = {};
          Object.keys(selectors).forEach((key) => {
            const element = selectors[key];
            result[key] = element
              ? {
                  text: element.textContent.trim(),
                  html: element.innerHTML.substring(0, 100),
                }
              : null;
          });

          return result;
        });

        logger.info("Product page elements:");
        Object.keys(productPageInfo).forEach((key) => {
          const info = productPageInfo[key];
          if (info) {
            logger.info(`  ${key}: "${info.text}"`);
          } else {
            logger.info(`  ${key}: NOT FOUND`);
          }
        });
      }
    } catch (error) {
      logger.error(`Debug failed: ${error.message}`);
      throw error;
    } finally {
      if (this.scraper) {
        await this.scraper.cleanup();
      }
    }
  }
}

// Run debug if called directly
if (require.main === module) {
  const debug = new ProductExtractionDebugger();
  debug.debugProductExtraction().catch((error) => {
    logger.error(`Debug failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = ProductExtractionDebugger;
