const sqlite3 = require("sqlite3").verbose();
const path = require("path");

class DatabaseSetup {
  constructor() {
    this.dbPath = path.join(__dirname, "products_working.sqlite");
    this.db = null;
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error("Error opening database:", err.message);
          reject(err);
        } else {
          console.log("Connected to SQLite database");
          resolve();
        }
      });
    });
  }

  async createTables() {
    const tables = [
      // Stores table
      `CREATE TABLE IF NOT EXISTS stores (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                base_url TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

      // Categories table
      `CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                url TEXT NOT NULL,
                parent_id INTEGER,
                level INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (store_id) REFERENCES stores (id),
                FOREIGN KEY (parent_id) REFERENCES categories (id),
                UNIQUE(store_id, url)
            )`,

      // Products table
      `CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_id INTEGER NOT NULL,
                category_id INTEGER,
                name TEXT NOT NULL,
                price REAL,
                original_price REAL,
                currency TEXT DEFAULT 'TND',
                product_url TEXT NOT NULL UNIQUE,
                image_url TEXT,
                description TEXT,
                brand TEXT,
                model TEXT,
                sku TEXT,
                in_stock BOOLEAN DEFAULT 1,
                stock_quantity INTEGER,
                availability_status TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_scraped DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (store_id) REFERENCES stores (id),
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )`,

      // Product availability
      `CREATE TABLE IF NOT EXISTS product_availability (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                is_available BOOLEAN DEFAULT 1,
                stock_quantity INTEGER,
                checked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )`,

      // Scraping logs
      `CREATE TABLE IF NOT EXISTS scraping_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_id INTEGER NOT NULL,
                category_id INTEGER,
                status TEXT NOT NULL,
                products_found INTEGER DEFAULT 0,
                products_updated INTEGER DEFAULT 0,
                errors_count INTEGER DEFAULT 0,
                start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                end_time DATETIME,
                error_message TEXT,
                FOREIGN KEY (store_id) REFERENCES stores (id),
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )`,

      // Price history
      `CREATE TABLE IF NOT EXISTS price_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                price REAL NOT NULL,
                original_price REAL,
                currency TEXT DEFAULT 'TND',
                recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )`,
    ];

    for (const tableSQL of tables) {
      await this.runQuery(tableSQL);
    }

    // Create indexes for better performance
    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_products_store_id ON products(store_id)",
      "CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id)",
      "CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)",
      "CREATE INDEX IF NOT EXISTS idx_products_price ON products(price)",
      "CREATE INDEX IF NOT EXISTS idx_products_last_scraped ON products(last_scraped)",
      "CREATE INDEX IF NOT EXISTS idx_categories_store_id ON categories(store_id)",
      "CREATE INDEX IF NOT EXISTS idx_availability_product_id ON product_availability(product_id)",
      "CREATE INDEX IF NOT EXISTS idx_price_history_product_id ON price_history(product_id)",
      "CREATE INDEX IF NOT EXISTS idx_scraping_logs_store_id ON scraping_logs(store_id)",
    ];

    for (const indexSQL of indexes) {
      await this.runQuery(indexSQL);
    }

    console.log("All tables and indexes created successfully");
  }

  async insertInitialData() {
    const stores = [
      { name: "Shoppi.tn", base_url: "https://shoppi.tn" },
      { name: "TunisiaNet", base_url: "https://www.tunisianet.com.tn" },
      { name: "MyTek", base_url: "https://www.mytek.tn" },
    ];

    for (const store of stores) {
      await this.runQuery(
        "INSERT OR IGNORE INTO stores (name, base_url) VALUES (?, ?)",
        [store.name, store.base_url]
      );
    }

    console.log("Initial store data inserted");
  }

  runQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (err) {
        if (err) {
          console.error("Database error:", err.message);
          reject(err);
        } else {
          resolve(this);
        }
      });
    });
  }

  close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error("Error closing database:", err.message);
          } else {
            console.log("Database connection closed");
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

// Run setup if called directly
if (require.main === module) {
  async function setup() {
    const dbSetup = new DatabaseSetup();
    try {
      await dbSetup.initialize();
      await dbSetup.createTables();
      await dbSetup.insertInitialData();
      console.log("Database setup completed successfully!");
    } catch (error) {
      console.error("Database setup failed:", error);
    } finally {
      await dbSetup.close();
    }
  }

  setup();
}

module.exports = DatabaseSetup;
