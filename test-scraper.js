#!/usr/bin/env node

const ShoppiScraper = require("./lib/ShoppiScraper");
const DatabaseSetup = require("./setup-database");
const winston = require("winston");
const path = require("path");

// Setup test logger
const logger = winston.createLogger({
  level: "debug",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level.toUpperCase()}] ${message}`;
    })
  ),
  transports: [new winston.transports.Console({ level: "debug" })],
});

class ScraperTester {
  constructor() {
    this.testResults = {
      databaseSetup: false,
      scraperInitialization: false,
      categoryDiscovery: false,
      productScraping: false,
      dataValidation: false,
    };
  }

  async runTests() {
    logger.info("Starting Scraper Test Suite");
    logger.info("=".repeat(50));

    try {
      // Test 1: Database Setup
      await this.testDatabaseSetup();

      // Test 2: Scraper Initialization
      await this.testScraperInitialization();

      // Test 3: Category Discovery (limited)
      await this.testCategoryDiscovery();

      // Test 4: Product Scraping (very limited)
      await this.testProductScraping();

      // Test 5: Data Validation
      await this.testDataValidation();
    } catch (error) {
      logger.error(`Test suite failed: ${error.message}`);
      throw error;
    }

    this.printTestResults();
  }

  async testDatabaseSetup() {
    logger.info("Test 1: Database Setup");

    try {
      const dbSetup = new DatabaseSetup();
      await dbSetup.initialize();
      await dbSetup.createTables();
      await dbSetup.insertInitialData();
      await dbSetup.close();

      this.testResults.databaseSetup = true;
      logger.info("✓ Database setup successful");
    } catch (error) {
      logger.error(`✗ Database setup failed: ${error.message}`);
      throw error;
    }
  }

  async testScraperInitialization() {
    logger.info("Test 2: Scraper Initialization");

    try {
      const scraper = new ShoppiScraper();
      await scraper.initialize();

      // Verify scraper properties
      if (!scraper.browser) throw new Error("Browser not initialized");
      if (!scraper.page) throw new Error("Page not initialized");
      if (!scraper.db) throw new Error("Database not initialized");
      if (!scraper.storeId) throw new Error("Store ID not found");

      await scraper.cleanup();

      this.testResults.scraperInitialization = true;
      logger.info("✓ Scraper initialization successful");
    } catch (error) {
      logger.error(`✗ Scraper initialization failed: ${error.message}`);
      throw error;
    }
  }

  async testCategoryDiscovery() {
    logger.info("Test 3: Category Discovery (Limited Test)");

    const scraper = new ShoppiScraper();

    try {
      await scraper.initialize();

      // Navigate to homepage and check for category elements
      await scraper.safeNavigate(scraper.baseUrl);
      await scraper.delay(3000);

      // Test category selector existence
      const categoryExists = await scraper.page.evaluate((selectors) => {
        const elements = document.querySelectorAll(selectors.mainMenu);
        return elements.length > 0;
      }, scraper.categorySelectors);

      if (!categoryExists) {
        logger.warn(
          "No categories found with current selectors - may need adjustment"
        );
        // Try alternative selectors
        const alternativeCategories = await scraper.page.evaluate(() => {
          const possibleSelectors = [
            "nav a",
            ".menu a",
            ".navigation a",
            ".category a",
            ".categories a",
            "header a",
          ];

          for (const selector of possibleSelectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
              return {
                selector: selector,
                count: elements.length,
                samples: Array.from(elements)
                  .slice(0, 3)
                  .map((el) => ({
                    text: el.textContent.trim(),
                    href: el.href,
                  })),
              };
            }
          }
          return null;
        });

        if (alternativeCategories) {
          logger.info(
            `Found alternative category selector: ${alternativeCategories.selector}`
          );
          logger.info(`Sample categories:`, alternativeCategories.samples);
        }
      } else {
        logger.info("✓ Category elements found with current selectors");
      }

      await scraper.cleanup();
      this.testResults.categoryDiscovery = true;
    } catch (error) {
      await scraper.cleanup();
      logger.error(`✗ Category discovery test failed: ${error.message}`);
      throw error;
    }
  }

  async testProductScraping() {
    logger.info("Test 4: Product Scraping (Very Limited Test)");

    const scraper = new ShoppiScraper();

    try {
      await scraper.initialize();

      // Try to find a category page to test product scraping
      await scraper.safeNavigate(scraper.baseUrl);
      await scraper.delay(2000);

      // Look for any category link
      const categoryUrl = await scraper.page.evaluate(() => {
        const links = document.querySelectorAll(
          'a[href*="category"], a[href*="categorie"]'
        );
        return links.length > 0 ? links[0].href : null;
      });

      if (categoryUrl) {
        logger.info(`Testing with category: ${categoryUrl}`);

        await scraper.safeNavigate(categoryUrl);
        await scraper.delay(2000);

        // Check for product elements
        const productExists = await scraper.page.evaluate((selectors) => {
          const elements = document.querySelectorAll(selectors.productGrid);
          return elements.length > 0;
        }, scraper.productSelectors);

        if (productExists) {
          logger.info("✓ Product elements found on category page");
        } else {
          logger.warn(
            "No products found with current selectors - may need adjustment"
          );

          // Try to find alternative product selectors
          const alternativeProducts = await scraper.page.evaluate(() => {
            const possibleSelectors = [
              ".product",
              ".item",
              ".card",
              ".listing",
              "[data-product]",
              ".product-item",
              ".product-card",
            ];

            for (const selector of possibleSelectors) {
              const elements = document.querySelectorAll(selector);
              if (elements.length > 0) {
                return {
                  selector: selector,
                  count: elements.length,
                };
              }
            }
            return null;
          });

          if (alternativeProducts) {
            logger.info(
              `Found alternative product selector: ${alternativeProducts.selector} (${alternativeProducts.count} items)`
            );
          }
        }
      } else {
        logger.warn("No category links found for product testing");
      }

      await scraper.cleanup();
      this.testResults.productScraping = true;
    } catch (error) {
      await scraper.cleanup();
      logger.error(`✗ Product scraping test failed: ${error.message}`);
      // Don't throw here, as this is expected to need adjustment
    }
  }

  async testDataValidation() {
    logger.info("Test 5: Data Validation");

    try {
      const sqlite3 = require("sqlite3").verbose();
      const path = require("path");

      const dbPath = path.join(__dirname, "products_working.sqlite");
      const db = new sqlite3.Database(dbPath);

      // Check if stores exist
      const stores = await new Promise((resolve, reject) => {
        db.all("SELECT * FROM stores", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      if (stores.length === 0) {
        throw new Error("No stores found in database");
      }

      logger.info(`✓ Found ${stores.length} stores in database`);
      stores.forEach((store) => {
        logger.info(`  - ${store.name}: ${store.base_url}`);
      });

      // Check table structure
      const tables = [
        "stores",
        "categories",
        "products",
        "product_availability",
        "scraping_logs",
        "price_history",
      ];
      for (const table of tables) {
        const result = await new Promise((resolve, reject) => {
          db.get(
            `SELECT name FROM sqlite_master WHERE type='table' AND name=?`,
            [table],
            (err, row) => {
              if (err) reject(err);
              else resolve(row);
            }
          );
        });

        if (!result) {
          throw new Error(`Table ${table} not found`);
        }
      }

      logger.info("✓ All required tables exist");

      // Close database
      await new Promise((resolve) => {
        db.close((err) => {
          if (err) logger.error("Error closing database:", err.message);
          resolve();
        });
      });

      this.testResults.dataValidation = true;
    } catch (error) {
      logger.error(`✗ Data validation failed: ${error.message}`);
      throw error;
    }
  }

  printTestResults() {
    logger.info("=".repeat(50));
    logger.info("TEST RESULTS SUMMARY");
    logger.info("=".repeat(50));

    const tests = [
      { name: "Database Setup", result: this.testResults.databaseSetup },
      {
        name: "Scraper Initialization",
        result: this.testResults.scraperInitialization,
      },
      {
        name: "Category Discovery",
        result: this.testResults.categoryDiscovery,
      },
      { name: "Product Scraping", result: this.testResults.productScraping },
      { name: "Data Validation", result: this.testResults.dataValidation },
    ];

    tests.forEach((test) => {
      const status = test.result ? "✓ PASS" : "✗ FAIL";
      logger.info(`${test.name}: ${status}`);
    });

    const passedTests = tests.filter((t) => t.result).length;
    const totalTests = tests.length;

    logger.info("=".repeat(50));
    logger.info(`Overall: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      logger.info("🎉 All tests passed! Ready for production scraping.");
    } else {
      logger.warn(
        "⚠️  Some tests failed. Review and adjust before production use."
      );
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new ScraperTester();
  tester.runTests().catch((error) => {
    logger.error(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = ScraperTester;
