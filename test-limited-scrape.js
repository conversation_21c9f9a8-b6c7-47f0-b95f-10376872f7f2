#!/usr/bin/env node

const ShoppiScraper = require('./lib/ShoppiScraper');
const winston = require('winston');

// Setup test logger
const logger = winston.createLogger({
    level: 'debug',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message }) => {
            return `${timestamp} [${level.toUpperCase()}] ${message}`;
        })
    ),
    transports: [
        new winston.transports.Console({ level: 'debug' })
    ]
});

class LimitedScrapeTest {
    constructor() {
        this.scraper = null;
    }

    async runLimitedTest() {
        logger.info('Starting Limited Scrape Test');
        logger.info('='.repeat(50));
        
        try {
            this.scraper = new ShoppiScraper();
            await this.scraper.initialize();
            
            // Test 1: Try to scrape categories
            logger.info('Test 1: Scraping categories...');
            const categories = await this.testCategoryExtraction();
            
            if (categories.length > 0) {
                logger.info(`✓ Found ${categories.length} categories`);
                
                // Test 2: Try to scrape products from first category
                logger.info('Test 2: Scraping products from first category...');
                const firstCategory = categories[0];
                await this.testProductExtraction(firstCategory);
            } else {
                logger.warn('No categories found - cannot test product extraction');
            }
            
        } catch (error) {
            logger.error(`Limited scrape test failed: ${error.message}`);
            throw error;
        } finally {
            if (this.scraper) {
                await this.scraper.cleanup();
            }
        }
    }

    async testCategoryExtraction() {
        try {
            await this.scraper.safeNavigate(this.scraper.baseUrl);
            await this.scraper.delay(3000);
            
            // Get main categories from navigation menu
            const mainCategories = await this.scraper.page.evaluate((selectors) => {
                const categoryElements = document.querySelectorAll(selectors.mainMenu);
                return Array.from(categoryElements).slice(0, 5).map(el => ({
                    name: el.textContent.trim(),
                    url: el.href,
                    level: 0
                }));
            }, this.scraper.categorySelectors);
            
            logger.info(`Found ${mainCategories.length} main categories:`);
            mainCategories.forEach((cat, index) => {
                logger.info(`  ${index + 1}. ${cat.name} - ${cat.url}`);
            });
            
            // Save categories to database for testing
            const categoriesWithIds = [];
            for (const category of mainCategories) {
                try {
                    const categoryId = await this.scraper.saveCategoryToDatabase(category);
                    categoriesWithIds.push({
                        ...category,
                        id: categoryId
                    });
                } catch (error) {
                    logger.error(`Error saving category ${category.name}: ${error.message}`);
                }
            }
            
            return categoriesWithIds;
            
        } catch (error) {
            logger.error(`Error extracting categories: ${error.message}`);
            return [];
        }
    }

    async testProductExtraction(category) {
        try {
            logger.info(`Testing product extraction from: ${category.name}`);
            
            await this.scraper.safeNavigate(category.url);
            await this.scraper.delay(3000);
            
            // Get product links from current page (limit to first 3)
            const productLinks = await this.scraper.page.evaluate((selectors) => {
                const productElements = document.querySelectorAll(selectors.productGrid);
                const links = [];
                
                productElements.forEach(element => {
                    const linkElement = element.querySelector(selectors.productLink);
                    if (linkElement && links.length < 3) {
                        links.push(linkElement.href);
                    }
                });
                
                return links;
            }, this.scraper.productSelectors);
            
            logger.info(`Found ${productLinks.length} product links on category page`);
            
            if (productLinks.length === 0) {
                // Try alternative selectors
                logger.info('Trying alternative product selectors...');
                const alternativeProducts = await this.scraper.page.evaluate(() => {
                    const possibleSelectors = [
                        '.product', '.item', '.card', '.listing',
                        '[data-product]', '.product-item', '.product-card',
                        '.col-md-3', '.col-sm-6', '.col-lg-4'
                    ];
                    
                    for (const selector of possibleSelectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            const links = [];
                            elements.forEach(el => {
                                const link = el.querySelector('a[href*="/site/details/"]');
                                if (link && links.length < 3) {
                                    links.push(link.href);
                                }
                            });
                            
                            if (links.length > 0) {
                                return {
                                    selector: selector,
                                    count: elements.length,
                                    links: links
                                };
                            }
                        }
                    }
                    return null;
                });
                
                if (alternativeProducts) {
                    logger.info(`Found alternative product selector: ${alternativeProducts.selector}`);
                    logger.info(`Total products: ${alternativeProducts.count}`);
                    productLinks.push(...alternativeProducts.links);
                }
            }
            
            // Test scraping details from first product
            if (productLinks.length > 0) {
                logger.info('Testing product detail extraction...');
                const firstProductUrl = productLinks[0];
                logger.info(`Scraping product: ${firstProductUrl}`);
                
                const productData = await this.scraper.scrapeProductDetails(firstProductUrl);
                
                if (productData) {
                    logger.info('✓ Successfully extracted product data:');
                    logger.info(`  Name: ${productData.name || 'N/A'}`);
                    logger.info(`  Price: ${productData.price || 'N/A'} ${productData.currency || 'TND'}`);
                    logger.info(`  URL: ${productData.productUrl}`);
                    logger.info(`  Image: ${productData.imageUrl || 'N/A'}`);
                    
                    // Try to save to database
                    try {
                        await this.scraper.saveProductToDatabase(productData, category.id);
                        logger.info('✓ Successfully saved product to database');
                    } catch (error) {
                        logger.error(`Error saving product to database: ${error.message}`);
                    }
                } else {
                    logger.warn('Failed to extract product data');
                }
            } else {
                logger.warn('No product links found for testing');
            }
            
        } catch (error) {
            logger.error(`Error testing product extraction: ${error.message}`);
        }
    }
}

// Run test if called directly
if (require.main === module) {
    const test = new LimitedScrapeTest();
    test.runLimitedTest().catch(error => {
        logger.error(`Test failed: ${error.message}`);
        process.exit(1);
    });
}

module.exports = LimitedScrapeTest;
