#!/usr/bin/env node

const ShoppiScraper = require("./lib/ShoppiScraper");
const TunisiaNetScraper = require("./lib/TunisiaNetScraper");
const MyTekScraper = require("./lib/MyTekScraper");
const DatabaseSetup = require("./setup-database");
const winston = require("winston");

// Setup logger
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level.toUpperCase()}] ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "logs/test-all-scrapers.log" }),
  ],
});

async function setupDatabase() {
  logger.info("Setting up database...");
  const dbSetup = new DatabaseSetup();

  try {
    await dbSetup.initialize();
    await dbSetup.createTables();
    await dbSetup.insertInitialData();
    logger.info("Database setup completed");
  } catch (error) {
    logger.error(`Database setup failed: ${error.message}`);
    throw error;
  } finally {
    await dbSetup.close();
  }
}

async function testScraper(ScraperClass, storeName) {
  logger.info(`\n=== Testing ${storeName} Scraper ===`);

  const scraper = new ScraperClass();
  let success = false;

  try {
    // Initialize the scraper first
    logger.info(`Initializing ${storeName} scraper...`);
    await scraper.initialize();

    // Test category scraping
    logger.info(`Testing category discovery for ${storeName}...`);
    const categories = await scraper.scrapeCategories();
    logger.info(`✅ Found ${categories.length} categories for ${storeName}`);

    if (categories.length > 0) {
      // Test product scraping from first category
      const firstCategory = categories[0];
      logger.info(
        `Testing product scraping from category: ${firstCategory.name}`
      );

      // Limit to just a few products for testing
      const originalLimit = scraper.productLimit;
      scraper.productLimit = 3; // Test with just 3 products

      await scraper.scrapeProductsFromCategory(
        firstCategory.url,
        firstCategory.id
      );

      // Restore original limit
      scraper.productLimit = originalLimit;

      logger.info(`✅ Product scraping test completed for ${storeName}`);
      success = true;
    } else {
      logger.warn(`⚠️  No categories found for ${storeName}`);
    }
  } catch (error) {
    logger.error(`❌ Error testing ${storeName}: ${error.message}`);
    success = false;
  } finally {
    try {
      await scraper.close();
    } catch (closeError) {
      logger.error(`Error closing ${storeName} scraper: ${closeError.message}`);
    }
  }

  return success;
}

async function main() {
  logger.info("🚀 Starting comprehensive scraper test...");

  try {
    // Setup database first
    await setupDatabase();

    const scrapers = [
      { class: ShoppiScraper, name: "Shoppi.tn" },
      { class: TunisiaNetScraper, name: "TunisiaNet" },
      { class: MyTekScraper, name: "MyTek" },
    ];

    const results = {};

    // Test each scraper
    for (const { class: ScraperClass, name } of scrapers) {
      results[name] = await testScraper(ScraperClass, name);

      // Wait between tests to be respectful
      if (name !== "MyTek") {
        // Don't wait after the last one
        logger.info("Waiting 10 seconds before next test...");
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    // Print summary
    logger.info("\n=== TEST SUMMARY ===");
    let successCount = 0;
    for (const [storeName, success] of Object.entries(results)) {
      const status = success ? "✅ PASS" : "❌ FAIL";
      logger.info(`${storeName}: ${status}`);
      if (success) successCount++;
    }

    logger.info(
      `\nOverall: ${successCount}/${
        Object.keys(results).length
      } scrapers working`
    );

    if (successCount === Object.keys(results).length) {
      logger.info("🎉 All scrapers are working correctly!");
    } else {
      logger.warn("⚠️  Some scrapers need attention");
    }
  } catch (error) {
    logger.error(`Test failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  logger.info("Received SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  logger.info("Received SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    logger.error(`Unhandled error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testScraper, setupDatabase };
