#!/usr/bin/env node

const ShoppiScraper = require('./lib/ShoppiScraper');
const winston = require('winston');

// Setup test logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message }) => {
            return `${timestamp} [${level.toUpperCase()}] ${message}`;
        })
    ),
    transports: [
        new winston.transports.Console({ level: 'info' })
    ]
});

class SimplifiedScraperTest {
    constructor() {
        this.scraper = null;
    }

    async runSimplifiedTest() {
        logger.info('Starting Simplified Scraper Test');
        logger.info('='.repeat(50));
        
        try {
            this.scraper = new ShoppiScraper();
            await this.scraper.initialize();
            
            // Test with just a few predefined categories (no subcategory discovery)
            const testCategories = [
                { name: 'Ordinateur Portable', url: 'https://shoppi.tn/site/produits/7/Ordinateur-Portable', level: 0 },
                { name: 'PC Portable', url: 'https://shoppi.tn/site/produits/9/PC-Portable', level: 0 },
                { name: 'MacBook', url: 'https://shoppi.tn/site/produits/148/MacBook', level: 0 }
            ];
            
            logger.info(`Testing with ${testCategories.length} predefined categories`);
            
            // Save categories to database
            const categoriesWithIds = [];
            for (const category of testCategories) {
                try {
                    const categoryId = await this.scraper.saveCategoryToDatabase(category);
                    categoriesWithIds.push({
                        ...category,
                        id: categoryId
                    });
                    logger.info(`✓ Saved category: ${category.name} (ID: ${categoryId})`);
                } catch (error) {
                    logger.error(`Error saving category ${category.name}: ${error.message}`);
                }
            }
            
            // Test product scraping from first category only
            if (categoriesWithIds.length > 0) {
                const testCategory = categoriesWithIds[0];
                logger.info(`\nTesting product scraping from: ${testCategory.name}`);
                
                await this.testProductScraping(testCategory);
            }
            
        } catch (error) {
            logger.error(`Simplified test failed: ${error.message}`);
            throw error;
        } finally {
            if (this.scraper) {
                await this.scraper.cleanup();
            }
        }
    }

    async testProductScraping(category) {
        try {
            logger.info(`Navigating to category: ${category.url}`);
            await this.scraper.safeNavigate(category.url);
            await this.scraper.delay(3000);
            
            // Get product links using our updated logic
            const productLinks = await this.scraper.page.evaluate((selectors) => {
                // Try direct approach first - get all detail links
                const detailLinks = document.querySelectorAll(selectors.productLink);
                const links = Array.from(detailLinks).map((link) => link.href);
                
                // If we have too many links, try to filter by product containers
                if (links.length > 100) {
                    const productElements = document.querySelectorAll(selectors.productGrid);
                    const filteredLinks = [];
                    
                    productElements.forEach((element) => {
                        const linkElement = element.querySelector(selectors.productLink);
                        if (linkElement) {
                            filteredLinks.push(linkElement.href);
                        }
                    });
                    
                    return filteredLinks.length > 0 ? filteredLinks : links.slice(0, 50);
                }
                
                return links;
            }, this.scraper.productSelectors);
            
            logger.info(`Found ${productLinks.length} product links`);
            
            if (productLinks.length > 0) {
                // Test scraping first 3 products
                const testProducts = productLinks.slice(0, 3);
                logger.info(`Testing product detail extraction for ${testProducts.length} products...`);
                
                for (let i = 0; i < testProducts.length; i++) {
                    const productUrl = testProducts[i];
                    logger.info(`\nScraping product ${i + 1}: ${productUrl}`);
                    
                    try {
                        const productData = await this.scraper.scrapeProductDetails(productUrl);
                        
                        if (productData) {
                            logger.info('✓ Successfully extracted product data:');
                            logger.info(`  Name: ${productData.name || 'N/A'}`);
                            logger.info(`  Price: ${productData.price || 'N/A'} TND`);
                            logger.info(`  Image: ${productData.imageUrl ? 'Found' : 'N/A'}`);
                            logger.info(`  Description: ${productData.description ? 'Found' : 'N/A'}`);
                            
                            // Try to save to database
                            try {
                                await this.scraper.saveProductToDatabase(productData, category.id);
                                logger.info('✓ Successfully saved product to database');
                            } catch (error) {
                                logger.error(`Error saving product to database: ${error.message}`);
                            }
                        } else {
                            logger.warn('Failed to extract product data');
                        }
                    } catch (error) {
                        logger.error(`Error scraping product ${i + 1}: ${error.message}`);
                    }
                    
                    // Small delay between products
                    await this.scraper.delay(1000);
                }
                
                // Test pagination (check if there's a next page)
                logger.info('\nTesting pagination...');
                const hasNextPage = await this.scraper.hasNextPage();
                logger.info(`Has next page: ${hasNextPage}`);
                
                if (hasNextPage) {
                    const page2Url = this.scraper.buildPageUrl(category.url, 2);
                    logger.info(`Next page URL would be: ${page2Url}`);
                }
                
            } else {
                logger.warn('No product links found for testing');
            }
            
        } catch (error) {
            logger.error(`Error testing product scraping: ${error.message}`);
        }
    }
}

// Run test if called directly
if (require.main === module) {
    const test = new SimplifiedScraperTest();
    test.runSimplifiedTest().catch(error => {
        logger.error(`Test failed: ${error.message}`);
        process.exit(1);
    });
}

module.exports = SimplifiedScraperTest;
