<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scraping Data Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .upload-section {
            padding: 40px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin: 20px 0;
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-input-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .file-input-button:hover {
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 30px;
        }
        
        .tab {
            padding: 15px 25px;
            background: none;
            border: none;
            font-size: 1em;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .product-card {
            background: white;
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .product-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #2d3748;
        }
        
        .product-price {
            font-size: 1.2em;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .product-store {
            background: #f7fafc;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            color: #666;
            display: inline-block;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ Scraping Data Viewer</h1>
            <p>Visualize your e-commerce scraping results</p>
        </div>
        
        <div class="upload-section">
            <h3>Load JSON Data File</h3>
            <div class="file-input-wrapper">
                <input type="file" id="jsonFile" class="file-input" accept=".json">
                <button class="file-input-button">Choose JSON File</button>
            </div>
            <p style="margin-top: 10px; color: #666;">
                Select a JSON file exported from the scraping system
            </p>
        </div>
        
        <div id="statsSection" class="stats-grid" style="display: none;">
            <!-- Stats will be populated here -->
        </div>
        
        <div id="contentSection" class="content-section" style="display: none;">
            <div class="tabs">
                <button class="tab active" onclick="showTab('overview')">Overview</button>
                <button class="tab" onclick="showTab('products')">Products</button>
                <button class="tab" onclick="showTab('stores')">Stores</button>
                <button class="tab" onclick="showTab('raw')">Raw JSON</button>
            </div>
            
            <div id="overview" class="tab-content active">
                <div id="overviewContent"></div>
            </div>
            
            <div id="products" class="tab-content">
                <div id="productsContent"></div>
            </div>
            
            <div id="stores" class="tab-content">
                <div id="storesContent"></div>
            </div>
            
            <div id="raw" class="tab-content">
                <div class="json-viewer" id="rawJson"></div>
            </div>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <h3>Loading data...</h3>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        let jsonData = null;
        
        document.getElementById('jsonFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                loadJsonFile(file);
            }
        });
        
        function loadJsonFile(file) {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    jsonData = JSON.parse(e.target.result);
                    displayData(jsonData);
                    document.getElementById('loading').style.display = 'none';
                } catch (error) {
                    showError('Invalid JSON file: ' + error.message);
                    document.getElementById('loading').style.display = 'none';
                }
            };
            reader.readAsText(file);
        }
        
        function displayData(data) {
            displayStats(data);
            displayOverview(data);
            displayProducts(data);
            displayStores(data);
            displayRawJson(data);
            
            document.getElementById('statsSection').style.display = 'grid';
            document.getElementById('contentSection').style.display = 'block';
        }
        
        function displayStats(data) {
            const statsSection = document.getElementById('statsSection');
            let statsHtml = '';
            
            if (data.export_info) {
                statsHtml += `
                    <div class="stat-card">
                        <div class="stat-number">${data.export_info.total_stores || 0}</div>
                        <div class="stat-label">Stores</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.export_info.total_categories || 0}</div>
                        <div class="stat-label">Categories</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.export_info.total_products || 0}</div>
                        <div class="stat-label">Products</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.export_info.total_price_records || 0}</div>
                        <div class="stat-label">Price Records</div>
                    </div>
                `;
            } else if (data.total_products) {
                statsHtml += `
                    <div class="stat-card">
                        <div class="stat-number">${data.total_products}</div>
                        <div class="stat-label">Products</div>
                    </div>
                `;
            }
            
            statsSection.innerHTML = statsHtml;
        }
        
        function displayOverview(data) {
            const overviewContent = document.getElementById('overviewContent');
            let html = '<h3>Data Overview</h3>';
            
            if (data.export_info) {
                html += `
                    <p><strong>Export Date:</strong> ${new Date(data.export_info.generated_at).toLocaleString()}</p>
                    <p><strong>Export Type:</strong> ${data.export_type || 'Complete Dataset'}</p>
                `;
            }
            
            if (data.products_by_store) {
                html += '<h4>Products by Store:</h4><ul>';
                Object.entries(data.products_by_store).forEach(([store, info]) => {
                    html += `<li><strong>${store}:</strong> ${info.total_products} products</li>`;
                });
                html += '</ul>';
            }
            
            overviewContent.innerHTML = html;
        }
        
        function displayProducts(data) {
            const productsContent = document.getElementById('productsContent');
            let products = [];
            
            if (data.products) {
                products = data.products;
            } else if (data.products_by_store) {
                Object.values(data.products_by_store).forEach(store => {
                    products = products.concat(store.products);
                });
            }
            
            if (products.length === 0) {
                productsContent.innerHTML = '<p>No products found in this dataset.</p>';
                return;
            }
            
            let html = `<h3>Products (${products.length})</h3><div class="product-grid">`;
            
            products.slice(0, 50).forEach(product => {
                html += `
                    <div class="product-card">
                        ${product.image_url ? `<img src="${product.image_url}" alt="${product.name}" class="product-image" onerror="this.style.display='none'">` : ''}
                        <div class="product-name">${product.name || 'Unnamed Product'}</div>
                        ${product.price ? `<div class="product-price">${product.price} TND</div>` : ''}
                        <div class="product-store">${product.store_name || 'Unknown Store'}</div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            if (products.length > 50) {
                html += `<p style="margin-top: 20px; text-align: center; color: #666;">Showing first 50 of ${products.length} products</p>`;
            }
            
            productsContent.innerHTML = html;
        }
        
        function displayStores(data) {
            const storesContent = document.getElementById('storesContent');
            let html = '<h3>Stores</h3>';
            
            if (data.stores) {
                data.stores.forEach(store => {
                    html += `
                        <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #eee; border-radius: 10px;">
                            <h4>${store.name}</h4>
                            <p><strong>URL:</strong> <a href="${store.url}" target="_blank">${store.url}</a></p>
                            <p><strong>Categories:</strong> ${store.categories ? store.categories.length : 0}</p>
                            <p><strong>Products:</strong> ${store.products ? store.products.length : 0}</p>
                        </div>
                    `;
                });
            } else {
                html += '<p>No store information available in this dataset.</p>';
            }
            
            storesContent.innerHTML = html;
        }
        
        function displayRawJson(data) {
            const rawJson = document.getElementById('rawJson');
            rawJson.textContent = JSON.stringify(data, null, 2);
        }
        
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    </script>
</body>
</html>
