const BaseScraper = require("./BaseScraper");

class ShoppiScraper extends BaseScraper {
  constructor() {
    super("Shoppi.tn", "https://shoppi.tn");
    this.categorySelectors = {
      mainMenu: 'a[href*="/site/produits/"]',
      categoryLinks: 'a[href*="/site/produits/"]',
      subcategoryLinks: 'a[href*="/site/produits/"]',
    };

    this.productSelectors = {
      productGrid: ".col-md-3, .col-sm-6, .product-item, .product-card",
      productLink: 'a[href*="/site/details/"]',
      productName: "h4, .product-name, .product-title, h3",
      productPrice: ".prix, .price, .product-price",
      productImage: "img",
      nextPageButton:
        '.pagination .next, .pagination a[aria-label="Next"], a[href*="page="]',
      paginationLinks: '.pagination a, a[href*="page="]',
    };

    this.productDetailSelectors = {
      name: "h1, .product-title, .title, h2",
      price: ".prix, .price, .product-price, .current-price",
      originalPrice: ".original-price, .old-price, .prix-original",
      description: ".product-description, .description, .desc, .product-desc",
      brand: ".brand, .product-brand, .marque",
      sku: ".sku, .product-code, .ref, .reference",
      availability: ".availability, .stock-status, .dispo, .stock",
      images:
        ".product-images img, .gallery img, .image img, .product-image img",
    };
  }

  async scrapeCategories() {
    this.logger.info("Starting category scraping for Shoppi.tn");

    await this.safeNavigate(this.baseUrl);

    // Wait for page to load
    await this.delay(2000);

    const categories = [];

    try {
      // Get main categories from navigation menu
      const mainCategories = await this.page.evaluate((selectors) => {
        const categoryElements = document.querySelectorAll(selectors.mainMenu);
        return Array.from(categoryElements).map((el) => ({
          name: el.textContent.trim(),
          url: el.href,
          level: 0,
        }));
      }, this.categorySelectors);

      this.logger.info(`Found ${mainCategories.length} main categories`);

      // Save categories to database and get their IDs
      for (const category of mainCategories) {
        try {
          const categoryId = await this.saveCategoryToDatabase(category);
          categories.push({
            ...category,
            id: categoryId,
          });

          // Try to find subcategories
          await this.scrapeSubcategories(category.url, categoryId, categories);
        } catch (error) {
          this.logger.error(
            `Error processing category ${category.name}: ${error.message}`
          );
          this.stats.errors++;
        }
      }
    } catch (error) {
      this.logger.error(`Error scraping main categories: ${error.message}`);
      throw error;
    }

    this.logger.info(`Total categories found: ${categories.length}`);
    return categories;
  }

  async scrapeSubcategories(categoryUrl, parentId, categories) {
    try {
      await this.safeNavigate(categoryUrl);
      await this.delay(1000);

      const subcategories = await this.page.evaluate((selectors) => {
        const subcategoryElements = document.querySelectorAll(
          selectors.subcategoryLinks
        );
        return Array.from(subcategoryElements).map((el) => ({
          name: el.textContent.trim(),
          url: el.href,
          level: 1,
        }));
      }, this.categorySelectors);

      for (const subcategory of subcategories) {
        const subcategoryId = await this.saveCategoryToDatabase(
          subcategory,
          parentId
        );
        categories.push({
          ...subcategory,
          id: subcategoryId,
          parentId: parentId,
        });
      }

      this.logger.info(
        `Found ${subcategories.length} subcategories for ${categoryUrl}`
      );
    } catch (error) {
      this.logger.warn(
        `Could not scrape subcategories for ${categoryUrl}: ${error.message}`
      );
    }
  }

  async saveCategoryToDatabase(category, parentId = null) {
    try {
      const result = await this.runQuery(
        `INSERT OR IGNORE INTO categories (store_id, name, url, parent_id, level) 
                 VALUES (?, ?, ?, ?, ?)`,
        [this.storeId, category.name, category.url, parentId, category.level]
      );

      // Get the category ID
      const row = await this.getQuery(
        "SELECT id FROM categories WHERE store_id = ? AND url = ?",
        [this.storeId, category.url]
      );

      return row ? row.id : result.lastID;
    } catch (error) {
      this.logger.error(
        `Error saving category ${category.name}: ${error.message}`
      );
      throw error;
    }
  }

  async scrapeProductsFromCategory(categoryUrl, categoryId) {
    this.logger.info(`Scraping products from category: ${categoryUrl}`);

    let currentPage = 1;
    let hasNextPage = true;
    let productsInCategory = 0;

    while (hasNextPage) {
      try {
        const pageUrl = this.buildPageUrl(categoryUrl, currentPage);
        await this.safeNavigate(pageUrl);
        await this.delay(2000);

        // Get product links from current page
        const productLinks = await this.page.evaluate((selectors) => {
          // Try direct approach first - get all detail links
          const detailLinks = document.querySelectorAll(selectors.productLink);
          const links = Array.from(detailLinks).map((link) => link.href);

          // If we have too many links, try to filter by product containers
          if (links.length > 100) {
            const productElements = document.querySelectorAll(
              selectors.productGrid
            );
            const filteredLinks = [];

            productElements.forEach((element) => {
              const linkElement = element.querySelector(selectors.productLink);
              if (linkElement) {
                filteredLinks.push(linkElement.href);
              }
            });

            return filteredLinks.length > 0
              ? filteredLinks
              : links.slice(0, 50);
          }

          return links;
        }, this.productSelectors);

        this.logger.info(
          `Found ${productLinks.length} products on page ${currentPage}`
        );
        productsInCategory += productLinks.length;

        // Scrape each product
        for (const productUrl of productLinks) {
          try {
            await this.scrapeAndSaveProduct(productUrl, categoryId);
            await this.delay(500); // Small delay between products
          } catch (error) {
            this.logger.error(
              `Error scraping product ${productUrl}: ${error.message}`
            );
            this.stats.errors++;
          }
        }

        // Check for next page
        hasNextPage = await this.hasNextPage();
        if (hasNextPage) {
          currentPage++;
          this.logger.info(`Moving to page ${currentPage}`);
        } else {
          this.logger.info(`No more pages found for category ${categoryUrl}`);
        }
      } catch (error) {
        this.logger.error(
          `Error on page ${currentPage} of category ${categoryUrl}: ${error.message}`
        );
        hasNextPage = false;
        this.stats.errors++;
      }
    }

    this.logger.info(
      `Completed category ${categoryUrl}. Total products: ${productsInCategory}`
    );
  }

  buildPageUrl(categoryUrl, page) {
    // Shoppi.tn pagination format: /site/produits/7/Ordinateur-Portable/page/2
    if (page === 1) {
      return categoryUrl;
    }

    // Remove trailing slash if present
    const cleanUrl = categoryUrl.replace(/\/$/, "");
    return `${cleanUrl}/page/${page}`;
  }

  async hasNextPage() {
    try {
      return await this.page.evaluate((selectors) => {
        const nextButton = document.querySelector(selectors.nextPageButton);
        return (
          nextButton &&
          !nextButton.disabled &&
          !nextButton.classList.contains("disabled")
        );
      }, this.productSelectors);
    } catch (error) {
      return false;
    }
  }

  async scrapeAndSaveProduct(productUrl, categoryId) {
    const productData = await this.scrapeProductDetails(productUrl);
    if (productData) {
      await this.saveProductToDatabase(productData, categoryId);
      this.stats.productsFound++;
    }
  }

  async scrapeProductDetails(productUrl) {
    try {
      await this.safeNavigate(productUrl);
      await this.delay(1000);

      const productData = await this.page.evaluate(
        (selectors, url) => {
          const getText = (selector) => {
            const element = document.querySelector(selector);
            return element ? element.textContent.trim() : null;
          };

          const getPrice = (selector) => {
            const element = document.querySelector(selector);
            if (!element) return null;

            const priceText = element.textContent.trim();
            const priceMatch = priceText.match(/[\d,]+\.?\d*/);
            return priceMatch
              ? parseFloat(priceMatch[0].replace(",", ""))
              : null;
          };

          const getImageUrl = (selector) => {
            const element = document.querySelector(selector);
            return element
              ? element.src || element.getAttribute("data-src")
              : null;
          };

          return {
            name: getText(selectors.name),
            price: getPrice(selectors.price),
            originalPrice: getPrice(selectors.originalPrice),
            description: getText(selectors.description),
            brand: getText(selectors.brand),
            sku: getText(selectors.sku),
            availability: getText(selectors.availability),
            imageUrl: getImageUrl(selectors.images),
            productUrl: url,
          };
        },
        this.productDetailSelectors,
        productUrl
      );

      // Clean and validate data
      if (!productData.name) {
        this.logger.warn(`No product name found for ${productUrl}`);
        return null;
      }

      return productData;
    } catch (error) {
      this.logger.error(
        `Error scraping product details for ${productUrl}: ${error.message}`
      );
      return null;
    }
  }

  async saveProductToDatabase(productData, categoryId) {
    try {
      // Check if product already exists
      const existingProduct = await this.getQuery(
        "SELECT id FROM products WHERE product_url = ?",
        [productData.productUrl]
      );

      if (existingProduct) {
        // Update existing product
        await this.runQuery(
          `UPDATE products SET 
                     name = ?, price = ?, original_price = ?, description = ?, 
                     brand = ?, sku = ?, availability_status = ?, image_url = ?,
                     updated_at = CURRENT_TIMESTAMP, last_scraped = CURRENT_TIMESTAMP
                     WHERE id = ?`,
          [
            productData.name,
            productData.price,
            productData.originalPrice,
            productData.description,
            productData.brand,
            productData.sku,
            productData.availability,
            productData.imageUrl,
            existingProduct.id,
          ]
        );

        this.stats.productsUpdated++;

        // Save price history
        if (productData.price) {
          await this.runQuery(
            "INSERT INTO price_history (product_id, price, original_price) VALUES (?, ?, ?)",
            [existingProduct.id, productData.price, productData.originalPrice]
          );
        }
      } else {
        // Insert new product
        const result = await this.runQuery(
          `INSERT INTO products 
                     (store_id, category_id, name, price, original_price, product_url, 
                      image_url, description, brand, sku, availability_status, in_stock)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            this.storeId,
            categoryId,
            productData.name,
            productData.price,
            productData.originalPrice,
            productData.productUrl,
            productData.imageUrl,
            productData.description,
            productData.brand,
            productData.sku,
            productData.availability,
            productData.availability &&
            !productData.availability.toLowerCase().includes("out of stock")
              ? 1
              : 0,
          ]
        );

        // Save initial price history
        if (productData.price && result.lastID) {
          await this.runQuery(
            "INSERT INTO price_history (product_id, price, original_price) VALUES (?, ?, ?)",
            [result.lastID, productData.price, productData.originalPrice]
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error saving product to database: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ShoppiScraper;
