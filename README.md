# Handy Scraping - E-commerce Data Collection Engine

A powerful Node.js-based web scraping system designed to autonomously collect product data from Tunisian e-commerce websites and populate a local SQLite database.

## 🎯 Project Vision

This project implements a **two-phase architecture**:

### Phase 1: Data Collection Engine (Current)
- **Autonomous scraping** of target websites (Shoppi.tn, TunisiaNet, MyTek)
- **Deep product extraction** including prices, availability, and city-level stock
- **Local database storage** for fast querying and historical tracking
- **Scheduled execution** for keeping data fresh

### Phase 2: User-Facing Dashboard (Future)
- Fast Vue.js frontend with instant search
- Express.js API serving data from local database
- No live scraping during user searches
- Historical price tracking and analytics

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Scraper       │    │   SQLite         │    │   Future API    │
│   Workers       │───▶│   Database       │───▶│   Server        │
│                 │    │                  │    │                 │
│ • Shoppi.tn     │    │ • Products       │    │ • Express.js    │
│ • TunisiaNet    │    │ • Categories     │    │ • Search API    │
│ • MyTek         │    │ • Price History  │    │ • Analytics     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd handy-scraping
npm install
```

2. **Set up the database:**
```bash
npm run setup-db
```

3. **Run tests to verify setup:**
```bash
npm test
```

4. **Start scraping:**
```bash
# Scrape all stores
npm start all

# Scrape specific store
npm start store shoppi
```

## 📋 Available Commands

```bash
# Database setup
node setup-database.js

# Run all scrapers
node crawler.js all

# Run specific store scraper
node crawler.js store shoppi
node crawler.js store tunisianet  # (when implemented)
node crawler.js store mytek       # (when implemented)

# Test scraper functionality
node test-scraper.js

# Using npm scripts
npm run setup-db     # Setup database
npm start all        # Run all scrapers
npm test            # Run tests
```

## 🗄️ Database Schema

### Core Tables
- **stores**: Store information (Shoppi.tn, TunisiaNet, MyTek)
- **categories**: Product categories and subcategories
- **products**: Main product data (name, price, URL, images, etc.)
- **product_availability**: City/region-specific stock information
- **price_history**: Historical price tracking
- **scraping_logs**: Execution logs and statistics

### Key Features
- **Automatic deduplication** based on product URLs
- **Price history tracking** for trend analysis
- **City-level availability** for location-based searches
- **Comprehensive logging** for monitoring and debugging

## 🔧 Configuration

Copy `.env.example` to `.env` and customize:

```bash
cp .env.example .env
```

Key settings:
- `SCRAPING_DELAY_MS`: Delay between requests (be respectful!)
- `HEADLESS_MODE`: Run browser in headless mode
- `LOG_LEVEL`: Logging verbosity
- `RETRY_ATTEMPTS`: Number of retries for failed requests

## 📊 Current Implementation Status

### ✅ Completed
- [x] Database schema and setup
- [x] Base scraper infrastructure
- [x] Shoppi.tn scraper implementation
- [x] Error handling and logging
- [x] Test suite
- [x] CLI interface

### 🚧 In Progress
- [ ] Shoppi.tn selector refinement (needs testing with live site)
- [ ] TunisiaNet scraper
- [ ] MyTek scraper

### 📋 Planned
- [ ] Automated scheduling (cron jobs)
- [ ] Email notifications for scraping results
- [ ] Data export functionality
- [ ] Performance optimizations
- [ ] Phase 2: Web dashboard

## 🧪 Testing

The test suite validates:
1. **Database Setup**: Table creation and initial data
2. **Scraper Initialization**: Browser and database connections
3. **Category Discovery**: Finding navigation elements
4. **Product Scraping**: Extracting product information
5. **Data Validation**: Database integrity

Run tests before production use:
```bash
npm test
```

## 📝 Logs

Logs are stored in the `logs/` directory:
- `crawler-main.log`: Main crawler execution
- `shoppi.tn-scraper.log`: Shoppi.tn specific logs
- Additional store logs as scrapers are added

## ⚠️ Important Notes

### Respectful Scraping
- Built-in delays between requests
- Respects robots.txt (manual verification recommended)
- User-agent rotation
- Error handling to avoid overwhelming servers

### Legal Compliance
- Ensure compliance with website terms of service
- Consider reaching out to websites for API access
- Use scraped data responsibly

### Performance
- Runs headless for efficiency
- Blocks unnecessary resources (images, CSS) during scraping
- Optimized database queries with proper indexing

## 🔮 Future Enhancements

### Phase 2: Web Dashboard
```bash
# Future structure
├── api/                 # Express.js API server
│   ├── routes/         # API endpoints
│   └── controllers/    # Business logic
├── frontend/           # Vue.js application
│   ├── components/     # Vue components
│   └── views/         # Page views
└── shared/            # Shared utilities
```

### Advanced Features
- **Price alerts**: Notify when prices drop
- **Inventory tracking**: Monitor stock changes
- **Competitor analysis**: Compare prices across stores
- **Mobile app**: React Native companion app

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Test your changes thoroughly
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Troubleshooting

### Common Issues

**Database locked error:**
```bash
# Stop all running scrapers and try again
pkill -f "node crawler.js"
```

**Puppeteer installation issues:**
```bash
# Reinstall puppeteer
npm uninstall puppeteer
npm install puppeteer
```

**Selector not found errors:**
- Run test suite to identify issues
- Website structure may have changed
- Update selectors in scraper files

### Getting Help

1. Check the logs in `logs/` directory
2. Run the test suite: `npm test`
3. Enable debug logging: Set `LOG_LEVEL=debug` in `.env`
4. Open an issue with detailed error information

---

**Happy Scraping! 🚀**
