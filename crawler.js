#!/usr/bin/env node

const ShoppiScraper = require("./lib/ShoppiScraper");
const TunisiaNetScraper = require("./lib/TunisiaNetScraper");
const MyTekScraper = require("./lib/MyTekScraper");
const DatabaseSetup = require("./setup-database");
const winston = require("winston");
const path = require("path");

// Setup main logger
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level.toUpperCase()}] ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      filename: path.join(__dirname, "logs", "crawler-main.log"),
    }),
  ],
});

class CrawlerManager {
  constructor() {
    this.scrapers = [];
    this.stats = {
      totalStores: 0,
      successfulStores: 0,
      failedStores: 0,
      totalProducts: 0,
      totalCategories: 0,
      startTime: null,
      endTime: null,
    };
  }

  async initialize() {
    logger.info("Initializing Handy Scraping Crawler");

    // Ensure database is set up
    await this.setupDatabase();

    // Initialize scrapers
    this.initializeScrapers();

    this.stats.startTime = new Date();
    this.stats.totalStores = this.scrapers.length;

    logger.info(`Initialized ${this.scrapers.length} scrapers`);
  }

  async setupDatabase() {
    logger.info("Setting up database...");
    const dbSetup = new DatabaseSetup();

    try {
      await dbSetup.initialize();
      await dbSetup.createTables();
      await dbSetup.insertInitialData();
      logger.info("Database setup completed");
    } catch (error) {
      logger.error(`Database setup failed: ${error.message}`);
      throw error;
    } finally {
      await dbSetup.close();
    }
  }

  initializeScrapers() {
    // Initialize all three scrapers
    this.scrapers = [
      new ShoppiScraper(),
      new TunisiaNetScraper(),
      new MyTekScraper(),
    ];
  }

  async runAllScrapers() {
    logger.info("Starting scraping process for all stores");

    for (const scraper of this.scrapers) {
      try {
        logger.info(`Starting scraper for ${scraper.storeName}`);
        await scraper.run();

        this.stats.successfulStores++;
        this.stats.totalProducts += scraper.stats.productsFound;
        this.stats.totalCategories += scraper.stats.categoriesFound;

        logger.info(`Completed scraper for ${scraper.storeName}`);
      } catch (error) {
        logger.error(
          `Scraper failed for ${scraper.storeName}: ${error.message}`
        );
        this.stats.failedStores++;
      }

      // Wait between scrapers to be respectful
      if (this.scrapers.indexOf(scraper) < this.scrapers.length - 1) {
        logger.info("Waiting 30 seconds before next scraper...");
        await this.delay(30000);
      }
    }
  }

  async runSingleStore(storeName) {
    logger.info(`Running scraper for specific store: ${storeName}`);

    const scraper = this.scrapers.find((s) =>
      s.storeName.toLowerCase().includes(storeName.toLowerCase())
    );

    if (!scraper) {
      throw new Error(`No scraper found for store: ${storeName}`);
    }

    try {
      await scraper.run();
      this.stats.successfulStores = 1;
      this.stats.totalProducts = scraper.stats.productsFound;
      this.stats.totalCategories = scraper.stats.categoriesFound;

      logger.info(`Completed scraper for ${scraper.storeName}`);
    } catch (error) {
      this.stats.failedStores = 1;
      logger.error(`Scraper failed for ${scraper.storeName}: ${error.message}`);
      throw error;
    }
  }

  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  printFinalStats() {
    this.stats.endTime = new Date();
    const duration = (this.stats.endTime - this.stats.startTime) / 1000;

    logger.info("=".repeat(50));
    logger.info("CRAWLING SESSION COMPLETED");
    logger.info("=".repeat(50));
    logger.info(
      `Total Duration: ${Math.round(duration)}s (${Math.round(duration / 60)}m)`
    );
    logger.info(`Stores Processed: ${this.stats.totalStores}`);
    logger.info(`Successful: ${this.stats.successfulStores}`);
    logger.info(`Failed: ${this.stats.failedStores}`);
    logger.info(`Total Categories: ${this.stats.totalCategories}`);
    logger.info(`Total Products: ${this.stats.totalProducts}`);
    logger.info("=".repeat(50));
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const storeName = args[1];

  const crawler = new CrawlerManager();

  try {
    await crawler.initialize();

    switch (command) {
      case "all":
        await crawler.runAllScrapers();
        break;

      case "store":
        if (!storeName) {
          throw new Error(
            "Store name required. Usage: node crawler.js store <store-name>"
          );
        }
        await crawler.runSingleStore(storeName);
        break;

      case "setup":
        logger.info("Database setup completed during initialization");
        break;

      default:
        logger.info("Usage:");
        logger.info("  node crawler.js all              - Run all scrapers");
        logger.info(
          "  node crawler.js store shoppi     - Run specific store scraper"
        );
        logger.info("  node crawler.js setup            - Setup database only");
        logger.info("");
        logger.info("Available stores: shoppi, tunisianet, mytek");
        return;
    }
  } catch (error) {
    logger.error(`Crawler failed: ${error.message}`);
    process.exit(1);
  } finally {
    crawler.printFinalStats();
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  logger.info("Received SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  logger.info("Received SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    logger.error(`Unhandled error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = CrawlerManager;
