#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

async function createNewDatabase() {
    console.log('🏗️ Creating new database...');
    
    const newDbPath = path.join(__dirname, 'products_new.sqlite');
    const oldDbPath = path.join(__dirname, 'products.sqlite');
    const backupJsonPath = path.join(__dirname, 'products.sqlite.backup.json');
    
    try {
        // Remove new database if it exists
        if (fs.existsSync(newDbPath)) {
            fs.unlinkSync(newDbPath);
        }
        
        // Create new database
        const db = new sqlite3.Database(newDbPath);
        
        console.log('📋 Creating tables...');
        
        // Create tables
        await new Promise((resolve, reject) => {
            db.serialize(() => {
                // Stores table
                db.run(`CREATE TABLE stores (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    base_url TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )`);
                
                // Categories table
                db.run(`CREATE TABLE categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    store_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    url TEXT,
                    parent_id INTEGER,
                    level INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (store_id) REFERENCES stores (id),
                    FOREIGN KEY (parent_id) REFERENCES categories (id)
                )`);
                
                // Products table
                db.run(`CREATE TABLE products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    store_id INTEGER NOT NULL,
                    category_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    price REAL,
                    original_price REAL,
                    product_url TEXT,
                    image_url TEXT,
                    description TEXT,
                    brand TEXT,
                    sku TEXT,
                    availability_status TEXT,
                    in_stock BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_scraped DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (store_id) REFERENCES stores (id),
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )`);
                
                // Price history table
                db.run(`CREATE TABLE price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    price REAL,
                    original_price REAL,
                    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )`);
                
                // Scraping logs table
                db.run(`CREATE TABLE scraping_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    store_id INTEGER NOT NULL,
                    category_id INTEGER,
                    status TEXT NOT NULL,
                    products_found INTEGER DEFAULT 0,
                    products_updated INTEGER DEFAULT 0,
                    errors_count INTEGER DEFAULT 0,
                    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    end_time DATETIME,
                    error_message TEXT,
                    FOREIGN KEY (store_id) REFERENCES stores (id),
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )`);
                
                // Product availability table
                db.run(`CREATE TABLE product_availability (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    is_available BOOLEAN DEFAULT 1,
                    stock_quantity INTEGER,
                    checked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )`, (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
        });
        
        console.log('📊 Creating indexes...');
        
        // Create indexes
        await new Promise((resolve, reject) => {
            db.serialize(() => {
                db.run('CREATE INDEX idx_products_store_id ON products (store_id)');
                db.run('CREATE INDEX idx_products_category_id ON products (category_id)');
                db.run('CREATE INDEX idx_products_price ON products (price)');
                db.run('CREATE INDEX idx_categories_store_id ON categories (store_id)');
                db.run('CREATE INDEX idx_price_history_product_id ON price_history (product_id)');
                db.run('CREATE INDEX idx_scraping_logs_store_id ON scraping_logs (store_id)', (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
        });
        
        console.log('🏪 Inserting initial store data...');
        
        // Insert initial stores
        await new Promise((resolve, reject) => {
            db.serialize(() => {
                const stmt = db.prepare('INSERT INTO stores (name, base_url) VALUES (?, ?)');
                stmt.run('Shoppi.tn', 'https://shoppi.tn');
                stmt.run('TunisiaNet', 'https://www.tunisianet.com.tn');
                stmt.run('MyTek', 'https://www.mytek.tn');
                stmt.finalize((err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
        });
        
        // Restore data from backup if it exists
        if (fs.existsSync(backupJsonPath)) {
            console.log('📥 Restoring data from backup...');
            
            const backupData = JSON.parse(fs.readFileSync(backupJsonPath, 'utf8'));
            
            // Restore categories
            if (backupData.categories && backupData.categories.length > 0) {
                for (const category of backupData.categories) {
                    await new Promise((resolve, reject) => {
                        db.run(
                            'INSERT OR REPLACE INTO categories (id, store_id, name, url, parent_id, level, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                            [category.id, category.store_id, category.name, category.url, category.parent_id, category.level, category.created_at, category.updated_at],
                            (err) => err ? reject(err) : resolve()
                        );
                    });
                }
                console.log(`✅ Restored ${backupData.categories.length} categories`);
            }
            
            // Restore products
            if (backupData.products && backupData.products.length > 0) {
                for (const product of backupData.products) {
                    await new Promise((resolve, reject) => {
                        db.run(
                            'INSERT OR REPLACE INTO products (id, store_id, category_id, name, price, original_price, product_url, image_url, description, brand, sku, availability_status, in_stock, created_at, updated_at, last_scraped) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                            [product.id, product.store_id, product.category_id, product.name, product.price, product.original_price, product.product_url, product.image_url, product.description, product.brand, product.sku, product.availability_status, product.in_stock, product.created_at, product.updated_at, product.last_scraped],
                            (err) => err ? reject(err) : resolve()
                        );
                    });
                }
                console.log(`✅ Restored ${backupData.products.length} products`);
            }
            
            // Restore price history
            if (backupData.price_history && backupData.price_history.length > 0) {
                for (const price of backupData.price_history) {
                    await new Promise((resolve, reject) => {
                        db.run(
                            'INSERT OR REPLACE INTO price_history (id, product_id, price, original_price, recorded_at) VALUES (?, ?, ?, ?, ?)',
                            [price.id, price.product_id, price.price, price.original_price, price.recorded_at],
                            (err) => err ? reject(err) : resolve()
                        );
                    });
                }
                console.log(`✅ Restored ${backupData.price_history.length} price records`);
            }
            
            // Restore scraping logs
            if (backupData.scraping_logs && backupData.scraping_logs.length > 0) {
                for (const log of backupData.scraping_logs) {
                    await new Promise((resolve, reject) => {
                        db.run(
                            'INSERT OR REPLACE INTO scraping_logs (id, store_id, category_id, status, products_found, products_updated, errors_count, start_time, end_time, error_message) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                            [log.id, log.store_id, log.category_id, log.status, log.products_found, log.products_updated, log.errors_count, log.start_time, log.end_time, log.error_message],
                            (err) => err ? reject(err) : resolve()
                        );
                    });
                }
                console.log(`✅ Restored ${backupData.scraping_logs.length} scraping logs`);
            }
        }
        
        // Close database
        await new Promise((resolve) => {
            db.close((err) => {
                if (err) console.error('Error closing database:', err.message);
                resolve();
            });
        });
        
        console.log('🔄 Replacing old database...');
        
        // Replace old database
        if (fs.existsSync(oldDbPath)) {
            const backupPath = oldDbPath + '.old.' + Date.now();
            try {
                fs.renameSync(oldDbPath, backupPath);
                console.log(`📦 Moved old database to ${backupPath}`);
            } catch (error) {
                console.log('⚠️ Could not move old database, but continuing...');
            }
        }
        
        fs.renameSync(newDbPath, oldDbPath);
        console.log('✅ New database is now active');
        
        // Set proper permissions
        fs.chmodSync(oldDbPath, 0o664);
        console.log('🔐 Set proper file permissions');
        
        console.log('🎉 Database creation completed successfully!');
        
        // Test the database
        console.log('🧪 Testing database...');
        const testDb = new sqlite3.Database(oldDbPath);
        
        await new Promise((resolve, reject) => {
            testDb.run('INSERT INTO scraping_logs (store_id, status) VALUES (1, "test")', (err) => {
                if (err) {
                    reject(err);
                } else {
                    testDb.run('DELETE FROM scraping_logs WHERE status = "test"', (err2) => {
                        testDb.close();
                        if (err2) reject(err2);
                        else resolve();
                    });
                }
            });
        });
        
        console.log('✅ Database write test passed!');
        
    } catch (error) {
        console.error('❌ Error creating database:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    createNewDatabase().catch(error => {
        console.error('Fatal error:', error.message);
        process.exit(1);
    });
}

module.exports = createNewDatabase;
