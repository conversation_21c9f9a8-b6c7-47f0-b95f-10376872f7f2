#!/usr/bin/env node

const cron = require('node-cron');
const { spawn } = require('child_process');
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Setup scheduler logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message }) => {
            return `${timestamp} [SCHEDULER-${level.toUpperCase()}] ${message}`;
        })
    ),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ 
            filename: path.join(__dirname, 'logs', 'scheduler.log') 
        })
    ]
});

class ScrapingScheduler {
    constructor() {
        this.isRunning = false;
        this.currentJob = null;
        this.schedules = {
            // Daily full scrape at 2 AM
            daily: {
                cron: '0 2 * * *',
                command: 'all',
                description: 'Daily full scrape of all stores'
            },
            // Quick update every 6 hours
            frequent: {
                cron: '0 */6 * * *',
                command: 'store shoppi',
                description: 'Frequent Shoppi.tn updates'
            },
            // Weekly deep clean at Sunday 1 AM
            weekly: {
                cron: '0 1 * * 0',
                command: 'cleanup',
                description: 'Weekly database cleanup'
            }
        };
    }

    start() {
        logger.info('Starting Handy Scraping Scheduler');
        
        // Schedule daily full scrape
        cron.schedule(this.schedules.daily.cron, () => {
            this.runScheduledJob('daily');
        }, {
            scheduled: true,
            timezone: "Africa/Tunis"
        });
        
        // Schedule frequent updates
        cron.schedule(this.schedules.frequent.cron, () => {
            this.runScheduledJob('frequent');
        }, {
            scheduled: true,
            timezone: "Africa/Tunis"
        });
        
        // Schedule weekly cleanup
        cron.schedule(this.schedules.weekly.cron, () => {
            this.runScheduledJob('weekly');
        }, {
            scheduled: true,
            timezone: "Africa/Tunis"
        });
        
        logger.info('Scheduler started with the following jobs:');
        Object.entries(this.schedules).forEach(([name, schedule]) => {
            logger.info(`  ${name}: ${schedule.cron} - ${schedule.description}`);
        });
        
        // Keep the process alive
        process.on('SIGINT', () => {
            logger.info('Scheduler shutting down...');
            if (this.currentJob) {
                logger.info('Terminating current job...');
                this.currentJob.kill('SIGTERM');
            }
            process.exit(0);
        });
    }

    async runScheduledJob(jobName) {
        if (this.isRunning) {
            logger.warn(`Skipping ${jobName} job - another job is already running`);
            return;
        }

        const schedule = this.schedules[jobName];
        if (!schedule) {
            logger.error(`Unknown job: ${jobName}`);
            return;
        }

        logger.info(`Starting scheduled job: ${jobName} - ${schedule.description}`);
        this.isRunning = true;

        try {
            await this.executeCrawlerCommand(schedule.command);
            logger.info(`Completed scheduled job: ${jobName}`);
        } catch (error) {
            logger.error(`Failed scheduled job: ${jobName} - ${error.message}`);
        } finally {
            this.isRunning = false;
            this.currentJob = null;
        }
    }

    executeCrawlerCommand(command) {
        return new Promise((resolve, reject) => {
            const args = command.split(' ');
            const crawlerPath = path.join(__dirname, 'crawler.js');
            
            logger.info(`Executing: node ${crawlerPath} ${args.join(' ')}`);
            
            this.currentJob = spawn('node', [crawlerPath, ...args], {
                cwd: __dirname,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stdout = '';
            let stderr = '';

            this.currentJob.stdout.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                // Log important output
                if (output.includes('ERROR') || output.includes('WARN') || output.includes('completed')) {
                    logger.info(`Crawler: ${output.trim()}`);
                }
            });

            this.currentJob.stderr.on('data', (data) => {
                const error = data.toString();
                stderr += error;
                logger.error(`Crawler Error: ${error.trim()}`);
            });

            this.currentJob.on('close', (code) => {
                if (code === 0) {
                    logger.info(`Crawler completed successfully`);
                    resolve({ stdout, stderr, code });
                } else {
                    logger.error(`Crawler exited with code ${code}`);
                    reject(new Error(`Crawler process failed with exit code ${code}`));
                }
            });

            this.currentJob.on('error', (error) => {
                logger.error(`Failed to start crawler: ${error.message}`);
                reject(error);
            });
        });
    }

    // Manual job execution
    async runManualJob(command) {
        if (this.isRunning) {
            throw new Error('Another job is already running');
        }

        logger.info(`Starting manual job: ${command}`);
        this.isRunning = true;

        try {
            const result = await this.executeCrawlerCommand(command);
            logger.info(`Manual job completed: ${command}`);
            return result;
        } catch (error) {
            logger.error(`Manual job failed: ${command} - ${error.message}`);
            throw error;
        } finally {
            this.isRunning = false;
            this.currentJob = null;
        }
    }

    getStatus() {
        return {
            isRunning: this.isRunning,
            currentJob: this.currentJob ? 'Running' : 'None',
            schedules: this.schedules,
            nextRuns: this.getNextRunTimes()
        };
    }

    getNextRunTimes() {
        // This is a simplified version - in production you'd use a proper cron parser
        const now = new Date();
        const nextRuns = {};
        
        // Calculate next run times (simplified)
        Object.entries(this.schedules).forEach(([name, schedule]) => {
            // For demo purposes, just show the cron expression
            nextRuns[name] = schedule.cron;
        });
        
        return nextRuns;
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    const scheduler = new ScrapingScheduler();
    
    switch (command) {
        case 'start':
            scheduler.start();
            break;
            
        case 'run':
            const jobCommand = args.slice(1).join(' ');
            if (!jobCommand) {
                console.log('Usage: node scheduler.js run <crawler-command>');
                console.log('Example: node scheduler.js run all');
                console.log('Example: node scheduler.js run store shoppi');
                return;
            }
            
            try {
                await scheduler.runManualJob(jobCommand);
                console.log('Manual job completed successfully');
            } catch (error) {
                console.error(`Manual job failed: ${error.message}`);
                process.exit(1);
            }
            break;
            
        case 'status':
            const status = scheduler.getStatus();
            console.log('Scheduler Status:');
            console.log(`  Running: ${status.isRunning}`);
            console.log(`  Current Job: ${status.currentJob}`);
            console.log('\nScheduled Jobs:');
            Object.entries(status.schedules).forEach(([name, schedule]) => {
                console.log(`  ${name}: ${schedule.cron} - ${schedule.description}`);
            });
            break;
            
        default:
            console.log('Handy Scraping Scheduler');
            console.log('');
            console.log('Usage:');
            console.log('  node scheduler.js start                    - Start the scheduler daemon');
            console.log('  node scheduler.js run <command>            - Run a manual job');
            console.log('  node scheduler.js status                   - Show scheduler status');
            console.log('');
            console.log('Examples:');
            console.log('  node scheduler.js start                    - Start automatic scheduling');
            console.log('  node scheduler.js run all                  - Run all scrapers manually');
            console.log('  node scheduler.js run store shoppi        - Run Shoppi scraper manually');
            console.log('');
            console.log('Scheduled Jobs:');
            Object.entries(scheduler.schedules).forEach(([name, schedule]) => {
                console.log(`  ${name}: ${schedule.cron} - ${schedule.description}`);
            });
            break;
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        logger.error(`Scheduler error: ${error.message}`);
        process.exit(1);
    });
}

module.exports = ScrapingScheduler;
