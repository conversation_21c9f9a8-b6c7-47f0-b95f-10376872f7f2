const puppeteer = require('puppeteer');
const sqlite3 = require('sqlite3').verbose();
const winston = require('winston');
const path = require('path');

class BaseScraper {
    constructor(storeName, baseUrl) {
        this.storeName = storeName;
        this.baseUrl = baseUrl;
        this.browser = null;
        this.page = null;
        this.db = null;
        this.storeId = null;
        
        // Setup logger
        this.logger = winston.createLogger({
            level: 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.printf(({ timestamp, level, message }) => {
                    return `${timestamp} [${level.toUpperCase()}] ${message}`;
                })
            ),
            transports: [
                new winston.transports.Console(),
                new winston.transports.File({ 
                    filename: path.join(__dirname, '..', 'logs', `${storeName.toLowerCase()}-scraper.log`) 
                })
            ]
        });

        // Scraping statistics
        this.stats = {
            categoriesFound: 0,
            productsFound: 0,
            productsUpdated: 0,
            errors: 0,
            startTime: null,
            endTime: null
        };
    }

    async initialize() {
        try {
            // Initialize database
            await this.initializeDatabase();
            
            // Initialize browser
            await this.initializeBrowser();
            
            this.logger.info(`${this.storeName} scraper initialized successfully`);
            this.stats.startTime = new Date();
            
        } catch (error) {
            this.logger.error(`Failed to initialize ${this.storeName} scraper: ${error.message}`);
            throw error;
        }
    }

    async initializeDatabase() {
        return new Promise((resolve, reject) => {
            const dbPath = path.join(__dirname, '..', 'products.sqlite');
            this.db = new sqlite3.Database(dbPath, async (err) => {
                if (err) {
                    reject(err);
                } else {
                    try {
                        // Get store ID
                        this.storeId = await this.getStoreId();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                }
            });
        });
    }

    async initializeBrowser() {
        this.browser = await puppeteer.launch({
            headless: 'new',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });
        
        this.page = await this.browser.newPage();
        
        // Set user agent to avoid detection
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // Set viewport
        await this.page.setViewport({ width: 1366, height: 768 });
        
        // Set request interception to block images and other resources for faster scraping
        await this.page.setRequestInterception(true);
        this.page.on('request', (req) => {
            const resourceType = req.resourceType();
            if (resourceType === 'image' || resourceType === 'stylesheet' || resourceType === 'font') {
                req.abort();
            } else {
                req.continue();
            }
        });
    }

    async getStoreId() {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT id FROM stores WHERE name = ?',
                [this.storeName],
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else if (row) {
                        resolve(row.id);
                    } else {
                        reject(new Error(`Store ${this.storeName} not found in database`));
                    }
                }
            );
        });
    }

    async runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve(this);
                }
            });
        });
    }

    async getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    async getAllQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async safeNavigate(url, retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                await this.page.goto(url, { 
                    waitUntil: 'networkidle2', 
                    timeout: 30000 
                });
                return true;
            } catch (error) {
                this.logger.warn(`Navigation attempt ${i + 1} failed for ${url}: ${error.message}`);
                if (i === retries - 1) {
                    throw error;
                }
                await this.delay(2000);
            }
        }
    }

    async logScrapingSession(categoryId = null, status = 'completed', errorMessage = null) {
        try {
            await this.runQuery(
                `INSERT INTO scraping_logs 
                (store_id, category_id, status, products_found, products_updated, errors_count, start_time, end_time, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    this.storeId,
                    categoryId,
                    status,
                    this.stats.productsFound,
                    this.stats.productsUpdated,
                    this.stats.errors,
                    this.stats.startTime.toISOString(),
                    new Date().toISOString(),
                    errorMessage
                ]
            );
        } catch (error) {
            this.logger.error(`Failed to log scraping session: ${error.message}`);
        }
    }

    async cleanup() {
        this.stats.endTime = new Date();
        const duration = (this.stats.endTime - this.stats.startTime) / 1000;
        
        this.logger.info(`Scraping completed for ${this.storeName}`);
        this.logger.info(`Duration: ${duration}s`);
        this.logger.info(`Categories found: ${this.stats.categoriesFound}`);
        this.logger.info(`Products found: ${this.stats.productsFound}`);
        this.logger.info(`Products updated: ${this.stats.productsUpdated}`);
        this.logger.info(`Errors: ${this.stats.errors}`);

        if (this.browser) {
            await this.browser.close();
        }
        
        if (this.db) {
            await new Promise((resolve) => {
                this.db.close((err) => {
                    if (err) {
                        this.logger.error(`Error closing database: ${err.message}`);
                    }
                    resolve();
                });
            });
        }
    }

    // Abstract methods to be implemented by specific scrapers
    async scrapeCategories() {
        throw new Error('scrapeCategories method must be implemented by subclass');
    }

    async scrapeProductsFromCategory(categoryUrl, categoryId) {
        throw new Error('scrapeProductsFromCategory method must be implemented by subclass');
    }

    async scrapeProductDetails(productUrl) {
        throw new Error('scrapeProductDetails method must be implemented by subclass');
    }

    // Main scraping workflow
    async run() {
        try {
            await this.initialize();
            
            this.logger.info(`Starting scraping for ${this.storeName}`);
            
            // Step 1: Scrape categories
            const categories = await this.scrapeCategories();
            this.stats.categoriesFound = categories.length;
            
            // Step 2: Scrape products from each category
            for (const category of categories) {
                try {
                    await this.scrapeProductsFromCategory(category.url, category.id);
                    await this.delay(1000); // Be respectful to the server
                } catch (error) {
                    this.logger.error(`Error scraping category ${category.name}: ${error.message}`);
                    this.stats.errors++;
                }
            }
            
            await this.logScrapingSession();
            
        } catch (error) {
            this.logger.error(`Scraping failed: ${error.message}`);
            await this.logScrapingSession(null, 'failed', error.message);
            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

module.exports = BaseScraper;
