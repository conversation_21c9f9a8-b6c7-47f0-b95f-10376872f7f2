#!/usr/bin/env node

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DataViewer {
    constructor() {
        this.dbPath = path.join(__dirname, 'products.sqlite');
        this.db = null;
    }

    async initialize() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    async getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    async getAllQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async showStats() {
        console.log('='.repeat(60));
        console.log('HANDY SCRAPING DATABASE STATISTICS');
        console.log('='.repeat(60));

        try {
            // Store statistics
            const stores = await this.getAllQuery('SELECT * FROM stores');
            console.log(`\n📊 STORES (${stores.length})`);
            stores.forEach(store => {
                console.log(`  • ${store.name}: ${store.base_url}`);
            });

            // Category statistics
            const categoryStats = await this.getAllQuery(`
                SELECT s.name as store_name, COUNT(c.id) as category_count
                FROM stores s
                LEFT JOIN categories c ON s.id = c.store_id
                GROUP BY s.id, s.name
            `);
            
            console.log(`\n📁 CATEGORIES BY STORE`);
            categoryStats.forEach(stat => {
                console.log(`  • ${stat.store_name}: ${stat.category_count} categories`);
            });

            // Product statistics
            const productStats = await this.getAllQuery(`
                SELECT s.name as store_name, COUNT(p.id) as product_count,
                       AVG(p.price) as avg_price, MIN(p.price) as min_price, MAX(p.price) as max_price
                FROM stores s
                LEFT JOIN products p ON s.id = p.store_id
                WHERE p.price IS NOT NULL
                GROUP BY s.id, s.name
            `);

            console.log(`\n🛍️ PRODUCTS BY STORE`);
            productStats.forEach(stat => {
                console.log(`  • ${stat.store_name}: ${stat.product_count} products`);
                if (stat.avg_price) {
                    console.log(`    Price range: ${stat.min_price?.toFixed(2)} - ${stat.max_price?.toFixed(2)} TND`);
                    console.log(`    Average price: ${stat.avg_price?.toFixed(2)} TND`);
                }
            });

            // Recent scraping activity
            const recentLogs = await this.getAllQuery(`
                SELECT s.name as store_name, sl.status, sl.products_found, sl.start_time
                FROM scraping_logs sl
                JOIN stores s ON sl.store_id = s.id
                ORDER BY sl.start_time DESC
                LIMIT 5
            `);

            console.log(`\n📝 RECENT SCRAPING ACTIVITY`);
            if (recentLogs.length === 0) {
                console.log('  No scraping activity recorded yet');
            } else {
                recentLogs.forEach(log => {
                    const date = new Date(log.start_time).toLocaleString();
                    console.log(`  • ${log.store_name}: ${log.status} (${log.products_found} products) - ${date}`);
                });
            }

            // Price trends (if any)
            const priceHistory = await this.getQuery(`
                SELECT COUNT(*) as total_price_records
                FROM price_history
            `);

            console.log(`\n💰 PRICE TRACKING`);
            console.log(`  • Total price records: ${priceHistory.total_price_records}`);

        } catch (error) {
            console.error('Error fetching statistics:', error.message);
        }
    }

    async showProducts(limit = 10, storeName = null) {
        console.log('='.repeat(60));
        console.log(`RECENT PRODUCTS ${storeName ? `FROM ${storeName.toUpperCase()}` : ''} (LIMIT ${limit})`);
        console.log('='.repeat(60));

        try {
            let query = `
                SELECT p.name, p.price, p.currency, s.name as store_name, 
                       p.product_url, p.last_scraped
                FROM products p
                JOIN stores s ON p.store_id = s.id
            `;
            
            let params = [];
            if (storeName) {
                query += ' WHERE s.name LIKE ?';
                params.push(`%${storeName}%`);
            }
            
            query += ' ORDER BY p.last_scraped DESC LIMIT ?';
            params.push(limit);

            const products = await this.getAllQuery(query, params);

            if (products.length === 0) {
                console.log('No products found');
                return;
            }

            products.forEach((product, index) => {
                console.log(`\n${index + 1}. ${product.name}`);
                console.log(`   Store: ${product.store_name}`);
                console.log(`   Price: ${product.price} ${product.currency || 'TND'}`);
                console.log(`   URL: ${product.product_url}`);
                console.log(`   Last scraped: ${new Date(product.last_scraped).toLocaleString()}`);
            });

        } catch (error) {
            console.error('Error fetching products:', error.message);
        }
    }

    async searchProducts(searchTerm, limit = 10) {
        console.log('='.repeat(60));
        console.log(`SEARCH RESULTS FOR: "${searchTerm}" (LIMIT ${limit})`);
        console.log('='.repeat(60));

        try {
            const products = await this.getAllQuery(`
                SELECT p.name, p.price, p.currency, s.name as store_name, 
                       p.product_url, p.description
                FROM products p
                JOIN stores s ON p.store_id = s.id
                WHERE p.name LIKE ? OR p.description LIKE ?
                ORDER BY p.price ASC
                LIMIT ?
            `, [`%${searchTerm}%`, `%${searchTerm}%`, limit]);

            if (products.length === 0) {
                console.log(`No products found matching "${searchTerm}"`);
                return;
            }

            products.forEach((product, index) => {
                console.log(`\n${index + 1}. ${product.name}`);
                console.log(`   Store: ${product.store_name}`);
                console.log(`   Price: ${product.price} ${product.currency || 'TND'}`);
                console.log(`   URL: ${product.product_url}`);
                if (product.description) {
                    const desc = product.description.substring(0, 100);
                    console.log(`   Description: ${desc}${product.description.length > 100 ? '...' : ''}`);
                }
            });

        } catch (error) {
            console.error('Error searching products:', error.message);
        }
    }

    async showCategories(storeName = null) {
        console.log('='.repeat(60));
        console.log(`CATEGORIES ${storeName ? `FROM ${storeName.toUpperCase()}` : ''}`);
        console.log('='.repeat(60));

        try {
            let query = `
                SELECT c.name, c.url, c.level, s.name as store_name,
                       COUNT(p.id) as product_count
                FROM categories c
                JOIN stores s ON c.store_id = s.id
                LEFT JOIN products p ON c.id = p.category_id
            `;
            
            let params = [];
            if (storeName) {
                query += ' WHERE s.name LIKE ?';
                params.push(`%${storeName}%`);
            }
            
            query += ' GROUP BY c.id ORDER BY s.name, c.level, c.name';

            const categories = await this.getAllQuery(query, params);

            if (categories.length === 0) {
                console.log('No categories found');
                return;
            }

            let currentStore = '';
            categories.forEach(category => {
                if (category.store_name !== currentStore) {
                    currentStore = category.store_name;
                    console.log(`\n📁 ${currentStore}:`);
                }
                
                const indent = '  '.repeat(category.level + 1);
                console.log(`${indent}• ${category.name} (${category.product_count} products)`);
            });

        } catch (error) {
            console.error('Error fetching categories:', error.message);
        }
    }

    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err.message);
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    const viewer = new DataViewer();
    
    try {
        await viewer.initialize();
        
        switch (command) {
            case 'stats':
                await viewer.showStats();
                break;
                
            case 'products':
                const limit = parseInt(args[1]) || 10;
                const storeName = args[2];
                await viewer.showProducts(limit, storeName);
                break;
                
            case 'search':
                const searchTerm = args[1];
                const searchLimit = parseInt(args[2]) || 10;
                if (!searchTerm) {
                    console.log('Usage: node data-viewer.js search <search-term> [limit]');
                    return;
                }
                await viewer.searchProducts(searchTerm, searchLimit);
                break;
                
            case 'categories':
                const categoryStore = args[1];
                await viewer.showCategories(categoryStore);
                break;
                
            default:
                console.log('Handy Scraping Data Viewer');
                console.log('');
                console.log('Usage:');
                console.log('  node data-viewer.js stats                     - Show database statistics');
                console.log('  node data-viewer.js products [limit] [store]  - Show recent products');
                console.log('  node data-viewer.js search <term> [limit]     - Search products');
                console.log('  node data-viewer.js categories [store]        - Show categories');
                console.log('');
                console.log('Examples:');
                console.log('  node data-viewer.js stats                     - Database overview');
                console.log('  node data-viewer.js products 20               - Show 20 recent products');
                console.log('  node data-viewer.js products 10 shoppi       - Show 10 products from Shoppi');
                console.log('  node data-viewer.js search laptop 15         - Search for laptops (15 results)');
                console.log('  node data-viewer.js categories shoppi        - Show Shoppi categories');
                break;
        }
        
    } catch (error) {
        console.error(`Data viewer error: ${error.message}`);
        process.exit(1);
    } finally {
        await viewer.close();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = DataViewer;
