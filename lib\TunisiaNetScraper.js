const BaseScraper = require('./BaseScraper');

class TunisiaNetScraper extends BaseScraper {
    constructor() {
        super('TunisiaNet', 'https://www.tunisianet.com.tn');
        this.categorySelectors = {
            mainMenu: 'nav a[href*="/"], .menu a[href*="/"]',
            categoryLinks: 'a[href*="/"]',
            subcategoryLinks: '.subcategory a[href*="/"]'
        };
        
        this.productSelectors = {
            productGrid: '.product-miniature, .product-item, article.product-miniature',
            productLink: 'a[href*=".html"]',
            productName: 'h3 a, .product-title a, h2 a',
            productPrice: '.price, .product-price',
            productImage: 'img',
            nextPageButton: '.pagination .next, a[aria-label="Next"]',
            paginationLinks: '.pagination a'
        };
        
        this.productDetailSelectors = {
            name: 'h1, .product-title, h2',
            price: '.current-price .price, .product-price, .price',
            originalPrice: '.regular-price .price, .old-price',
            description: '.product-description, #description, .description',
            brand: '.product-brand, .brand',
            sku: '.product-reference, .reference, .sku',
            availability: '.product-availability, .availability, .stock-status',
            images: '.product-cover img, .product-images img, .gallery img'
        };
    }

    async scrapeCategories() {
        this.logger.info('Starting category scraping for TunisiaNet');
        
        await this.safeNavigate(this.baseUrl);
        await this.delay(3000);
        
        const categories = [];
        
        try {
            // Get main categories from navigation menu
            const mainCategories = await this.page.evaluate(() => {
                const categoryElements = document.querySelectorAll('nav a[href*="/"]');
                const categories = [];
                
                categoryElements.forEach(el => {
                    const href = el.href;
                    const text = el.textContent.trim();
                    
                    // Filter for category URLs (contain numbers and category names)
                    if (href.includes('/') && 
                        text.length > 2 && 
                        !href.includes('javascript') &&
                        !href.includes('mailto') &&
                        !href.includes('tel:') &&
                        !href.includes('#') &&
                        (href.includes('-') || /\d/.test(href))) {
                        categories.push({
                            name: text,
                            url: href,
                            level: 0
                        });
                    }
                });
                
                // Remove duplicates
                const uniqueCategories = [];
                const seen = new Set();
                categories.forEach(cat => {
                    if (!seen.has(cat.url)) {
                        seen.add(cat.url);
                        uniqueCategories.push(cat);
                    }
                });
                
                return uniqueCategories.slice(0, 20); // Limit to first 20 categories
            });
            
            this.logger.info(`Found ${mainCategories.length} main categories`);
            
            // Save categories to database and get their IDs
            for (const category of mainCategories) {
                try {
                    const categoryId = await this.saveCategoryToDatabase(category);
                    categories.push({
                        ...category,
                        id: categoryId
                    });
                    
                } catch (error) {
                    this.logger.error(`Error processing category ${category.name}: ${error.message}`);
                    this.stats.errors++;
                }
            }
            
        } catch (error) {
            this.logger.error(`Error scraping main categories: ${error.message}`);
            throw error;
        }
        
        this.logger.info(`Total categories found: ${categories.length}`);
        return categories;
    }

    async saveCategoryToDatabase(category, parentId = null) {
        try {
            const result = await this.runQuery(
                `INSERT OR IGNORE INTO categories (store_id, name, url, parent_id, level) 
                 VALUES (?, ?, ?, ?, ?)`,
                [this.storeId, category.name, category.url, parentId, category.level]
            );
            
            // Get the category ID
            const row = await this.getQuery(
                'SELECT id FROM categories WHERE store_id = ? AND url = ?',
                [this.storeId, category.url]
            );
            
            return row ? row.id : result.lastID;
        } catch (error) {
            this.logger.error(`Error saving category ${category.name}: ${error.message}`);
            throw error;
        }
    }

    async scrapeProductsFromCategory(categoryUrl, categoryId) {
        this.logger.info(`Scraping products from category: ${categoryUrl}`);
        
        let currentPage = 1;
        let hasNextPage = true;
        let productsInCategory = 0;
        
        while (hasNextPage && currentPage <= 5) { // Limit to 5 pages per category
            try {
                const pageUrl = this.buildPageUrl(categoryUrl, currentPage);
                await this.safeNavigate(pageUrl);
                await this.delay(3000);
                
                // Get product links from current page
                const productLinks = await this.page.evaluate((selectors) => {
                    // Try direct approach first - get all product links
                    const detailLinks = document.querySelectorAll(selectors.productLink);
                    const links = Array.from(detailLinks).map((link) => link.href);
                    
                    // Filter for actual product URLs
                    const productUrls = links.filter(url => 
                        url.includes('.html') && 
                        !url.includes('javascript') &&
                        url.includes(window.location.hostname)
                    );
                    
                    return productUrls.slice(0, 50); // Limit to 50 products per page
                }, this.productSelectors);
                
                this.logger.info(`Found ${productLinks.length} products on page ${currentPage}`);
                productsInCategory += productLinks.length;
                
                // Scrape each product
                for (const productUrl of productLinks) {
                    try {
                        await this.scrapeAndSaveProduct(productUrl, categoryId);
                        await this.delay(1000); // Delay between products
                    } catch (error) {
                        this.logger.error(`Error scraping product ${productUrl}: ${error.message}`);
                        this.stats.errors++;
                    }
                }
                
                // Check for next page
                hasNextPage = await this.hasNextPage();
                if (hasNextPage) {
                    currentPage++;
                    this.logger.info(`Moving to page ${currentPage}`);
                } else {
                    this.logger.info(`No more pages found for category ${categoryUrl}`);
                }
                
            } catch (error) {
                this.logger.error(`Error on page ${currentPage} of category ${categoryUrl}: ${error.message}`);
                hasNextPage = false;
                this.stats.errors++;
            }
        }
        
        this.logger.info(`Completed category ${categoryUrl}. Total products: ${productsInCategory}`);
    }

    buildPageUrl(categoryUrl, page) {
        // TunisiaNet pagination format: ?page=2
        if (page === 1) {
            return categoryUrl;
        }
        
        const separator = categoryUrl.includes('?') ? '&' : '?';
        return `${categoryUrl}${separator}page=${page}`;
    }

    async hasNextPage() {
        try {
            return await this.page.evaluate((selectors) => {
                const nextButton = document.querySelector(selectors.nextPageButton);
                return nextButton && !nextButton.disabled && !nextButton.classList.contains('disabled');
            }, this.productSelectors);
        } catch (error) {
            return false;
        }
    }

    async scrapeAndSaveProduct(productUrl, categoryId) {
        const productData = await this.scrapeProductDetails(productUrl);
        if (productData) {
            await this.saveProductToDatabase(productData, categoryId);
            this.stats.productsFound++;
        }
    }

    async scrapeProductDetails(productUrl) {
        try {
            await this.safeNavigate(productUrl);
            await this.delay(2000);
            
            const productData = await this.page.evaluate((selectors, url) => {
                const getText = (selector) => {
                    const element = document.querySelector(selector);
                    return element ? element.textContent.trim() : null;
                };
                
                const getPrice = (selector) => {
                    // Try multiple price selectors
                    const selectors_list = [selector, '.current-price', '.price', '.product-price'];
                    
                    for (const sel of selectors_list) {
                        const elements = document.querySelectorAll(sel);
                        for (const element of elements) {
                            const priceText = element.textContent.trim();
                            // Look for patterns like "729,000 DT" or "729.000 DT"
                            const priceMatch = priceText.match(/(\d{1,3}(?:[,.]?\d{3})*(?:[,.]?\d{3})?)\s*DT/i);
                            if (priceMatch) {
                                const cleanPrice = priceMatch[1].replace(/[,]/g, '');
                                return parseFloat(cleanPrice);
                            }
                        }
                    }
                    return null;
                };
                
                const getImageUrl = (selector) => {
                    // Try multiple image selectors
                    const selectors_list = [
                        selector,
                        "img",
                        ".product-cover img",
                        ".product-images img",
                    ];
                    
                    for (const sel of selectors_list) {
                        const element = document.querySelector(sel);
                        if (element) {
                            const src =
                                element.src ||
                                element.getAttribute("data-src") ||
                                element.getAttribute("data-original");
                            if (src && src.includes("http")) {
                                return src;
                            }
                        }
                    }
                    return null;
                };
                
                return {
                    name: getText(selectors.name),
                    price: getPrice(selectors.price),
                    originalPrice: getPrice(selectors.originalPrice),
                    description: getText(selectors.description),
                    brand: getText(selectors.brand),
                    sku: getText(selectors.sku),
                    availability: getText(selectors.availability),
                    imageUrl: getImageUrl(selectors.images),
                    productUrl: url
                };
            }, this.productDetailSelectors, productUrl);
            
            // Clean and validate data
            if (!productData.name) {
                this.logger.warn(`No product name found for ${productUrl}`);
                return null;
            }
            
            return productData;
            
        } catch (error) {
            this.logger.error(`Error scraping product details for ${productUrl}: ${error.message}`);
            return null;
        }
    }

    async saveProductToDatabase(productData, categoryId) {
        try {
            // Check if product already exists
            const existingProduct = await this.getQuery(
                'SELECT id FROM products WHERE product_url = ?',
                [productData.productUrl]
            );
            
            if (existingProduct) {
                // Update existing product
                await this.runQuery(
                    `UPDATE products SET 
                     name = ?, price = ?, original_price = ?, description = ?, 
                     brand = ?, sku = ?, availability_status = ?, image_url = ?,
                     updated_at = CURRENT_TIMESTAMP, last_scraped = CURRENT_TIMESTAMP
                     WHERE id = ?`,
                    [
                        productData.name,
                        productData.price,
                        productData.originalPrice,
                        productData.description,
                        productData.brand,
                        productData.sku,
                        productData.availability,
                        productData.imageUrl,
                        existingProduct.id
                    ]
                );
                
                this.stats.productsUpdated++;
                
                // Save price history
                if (productData.price) {
                    await this.runQuery(
                        'INSERT INTO price_history (product_id, price, original_price) VALUES (?, ?, ?)',
                        [existingProduct.id, productData.price, productData.originalPrice]
                    );
                }
                
            } else {
                // Insert new product
                const result = await this.runQuery(
                    `INSERT INTO products 
                     (store_id, category_id, name, price, original_price, product_url, 
                      image_url, description, brand, sku, availability_status, in_stock)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        this.storeId,
                        categoryId,
                        productData.name,
                        productData.price,
                        productData.originalPrice,
                        productData.productUrl,
                        productData.imageUrl,
                        productData.description,
                        productData.brand,
                        productData.sku,
                        productData.availability,
                        productData.availability && !productData.availability.toLowerCase().includes('out of stock') ? 1 : 0
                    ]
                );
                
                // Save initial price history
                if (productData.price && result.lastID) {
                    await this.runQuery(
                        'INSERT INTO price_history (product_id, price, original_price) VALUES (?, ?, ?)',
                        [result.lastID, productData.price, productData.originalPrice]
                    );
                }
            }
            
        } catch (error) {
            this.logger.error(`Error saving product to database: ${error.message}`);
            throw error;
        }
    }
}

module.exports = TunisiaNetScraper;
